import 'package:eschool_teacher/data/models/subject.dart';
import 'package:eschool_teacher/data/models/subject_level.dart';

class SubjectTaught {
  final Subject subject;
  final List<SubjectLevel> levels;
  final double pricePerHour;

  SubjectTaught({
    required this.subject,
    required this.levels,
    required this.pricePerHour,
  });

  String get formattedPrice => '${pricePerHour.toStringAsFixed(2)} ريال/ساعة';

  SubjectTaught copyWith({
    Subject? subject,
    List<SubjectLevel>? levels,
    double? pricePerHour,
  }) {
    return SubjectTaught(
      subject: subject ?? this.subject,
      levels: levels ?? this.levels,
      pricePerHour: pricePerHour ?? this.pricePerHour,
    );
  }

  factory SubjectTaught.fromJson(Map<String, dynamic> json) {
    return SubjectTaught(
      subject: Subject.fromJson(json['subject'] ?? {}),
      levels: (json['levels'] as List? ?? [])
          .map((level) => SubjectLevel.fromJson(level))
          .toList(),
      pricePerHour: (json['price_per_hour'] ?? 0.0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'subject': subject.toJson(),
      'levels': levels.map((level) => level.toJson()).toList(),
      'price_per_hour': pricePerHour,
    };
  }
}

class TeacherProfile {
  final String id;
  final String firstName;
  final String lastName;
  final String email;
  final String mobile;
  final String? profileImageUrl;
  final String? bio;
  final String qualification;
  final int yearsOfExperience;
  final List<SubjectTaught> subjectsTaught;
  final String? iban;
  final String? bankName;
  final String? accountHolderName;
  final int weeklyLessonLimit;
  final int lessonsGiven;
  final double rating;
  final int totalReviews;
  final bool isActive;
  final bool isVerified;
  final DateTime joinedAt;
  final DateTime? lastActiveAt;

  TeacherProfile({
    required this.id,
    required this.firstName,
    required this.lastName,
    required this.email,
    required this.mobile,
    this.profileImageUrl,
    this.bio,
    required this.qualification,
    this.yearsOfExperience = 0,
    this.subjectsTaught = const [],
    this.iban,
    this.bankName,
    this.accountHolderName,
    this.weeklyLessonLimit = 20,
    this.lessonsGiven = 0,
    this.rating = 0.0,
    this.totalReviews = 0,
    this.isActive = true,
    this.isVerified = false,
    required this.joinedAt,
    this.lastActiveAt,
  });

  // Helper getters
  String get fullName => '$firstName $lastName';
  String get displayName => '$firstName $lastName';
  String get formattedRating => rating.toStringAsFixed(1);
  bool get hasIban => iban != null && iban!.isNotEmpty;
  bool get canReceivePayments => hasIban && isVerified;
  
  String get experienceText {
    if (yearsOfExperience == 0) return 'مدرس جديد';
    if (yearsOfExperience == 1) return 'سنة واحدة من الخبرة';
    if (yearsOfExperience == 2) return 'سنتان من الخبرة';
    if (yearsOfExperience <= 10) return '$yearsOfExperience سنوات من الخبرة';
    return '$yearsOfExperience سنة من الخبرة';
  }

  String get lessonsGivenText {
    if (lessonsGiven == 0) return 'لم يعطِ دروساً بعد';
    if (lessonsGiven == 1) return 'درس واحد';
    if (lessonsGiven == 2) return 'درسان';
    if (lessonsGiven <= 10) return '$lessonsGiven دروس';
    return '$lessonsGiven درساً';
  }

  List<String> get subjectNames => 
      subjectsTaught.map((st) => st.subject.name).toList();

  double get averagePrice {
    if (subjectsTaught.isEmpty) return 0.0;
    final total = subjectsTaught.fold(0.0, (sum, st) => sum + st.pricePerHour);
    return total / subjectsTaught.length;
  }

  // Copy constructor for immutability
  TeacherProfile copyWith({
    String? id,
    String? firstName,
    String? lastName,
    String? email,
    String? mobile,
    String? profileImageUrl,
    String? bio,
    String? qualification,
    int? yearsOfExperience,
    List<SubjectTaught>? subjectsTaught,
    String? iban,
    String? bankName,
    String? accountHolderName,
    int? weeklyLessonLimit,
    int? lessonsGiven,
    double? rating,
    int? totalReviews,
    bool? isActive,
    bool? isVerified,
    DateTime? joinedAt,
    DateTime? lastActiveAt,
  }) {
    return TeacherProfile(
      id: id ?? this.id,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      email: email ?? this.email,
      mobile: mobile ?? this.mobile,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      bio: bio ?? this.bio,
      qualification: qualification ?? this.qualification,
      yearsOfExperience: yearsOfExperience ?? this.yearsOfExperience,
      subjectsTaught: subjectsTaught ?? this.subjectsTaught,
      iban: iban ?? this.iban,
      bankName: bankName ?? this.bankName,
      accountHolderName: accountHolderName ?? this.accountHolderName,
      weeklyLessonLimit: weeklyLessonLimit ?? this.weeklyLessonLimit,
      lessonsGiven: lessonsGiven ?? this.lessonsGiven,
      rating: rating ?? this.rating,
      totalReviews: totalReviews ?? this.totalReviews,
      isActive: isActive ?? this.isActive,
      isVerified: isVerified ?? this.isVerified,
      joinedAt: joinedAt ?? this.joinedAt,
      lastActiveAt: lastActiveAt ?? this.lastActiveAt,
    );
  }

  factory TeacherProfile.fromJson(Map<String, dynamic> json) {
    return TeacherProfile(
      id: json['id'] ?? '',
      firstName: json['first_name'] ?? '',
      lastName: json['last_name'] ?? '',
      email: json['email'] ?? '',
      mobile: json['mobile'] ?? '',
      profileImageUrl: json['profile_image_url'],
      bio: json['bio'],
      qualification: json['qualification'] ?? '',
      yearsOfExperience: json['years_of_experience'] ?? 0,
      subjectsTaught: (json['subjects_taught'] as List? ?? [])
          .map((subject) => SubjectTaught.fromJson(subject))
          .toList(),
      iban: json['iban'],
      bankName: json['bank_name'],
      accountHolderName: json['account_holder_name'],
      weeklyLessonLimit: json['weekly_lesson_limit'] ?? 20,
      lessonsGiven: json['lessons_given'] ?? 0,
      rating: (json['rating'] ?? 0.0).toDouble(),
      totalReviews: json['total_reviews'] ?? 0,
      isActive: json['is_active'] ?? true,
      isVerified: json['is_verified'] ?? false,
      joinedAt: DateTime.parse(json['joined_at']),
      lastActiveAt: json['last_active_at'] != null 
          ? DateTime.parse(json['last_active_at'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'first_name': firstName,
      'last_name': lastName,
      'email': email,
      'mobile': mobile,
      'profile_image_url': profileImageUrl,
      'bio': bio,
      'qualification': qualification,
      'years_of_experience': yearsOfExperience,
      'subjects_taught': subjectsTaught.map((st) => st.toJson()).toList(),
      'iban': iban,
      'bank_name': bankName,
      'account_holder_name': accountHolderName,
      'weekly_lesson_limit': weeklyLessonLimit,
      'lessons_given': lessonsGiven,
      'rating': rating,
      'total_reviews': totalReviews,
      'is_active': isActive,
      'is_verified': isVerified,
      'joined_at': joinedAt.toIso8601String(),
      'last_active_at': lastActiveAt?.toIso8601String(),
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TeacherProfile && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'TeacherProfile(id: $id, fullName: $fullName, email: $email, lessonsGiven: $lessonsGiven)';
  }
}
