# Design System Usage Guide

## 🚀 Quick Start

The eSchool app now has a complete design system! Here's how to use it in your screens and widgets.

## 📦 Import Design System

```dart
// Core design system (colors, typography, spacing, etc.)
import 'package:eschool/ui/design_system/app_design_system.dart';

// All components (buttons, text fields, bottom sheets, etc.)
import 'package:eschool/ui/design_system/components/app_components.dart';
```

## 🎨 Design Tokens

### Colors
```dart
// Primary colors
AppColors.primaryColor          // #22577A (main brand color)
AppColors.secondaryColor        // #57CC99 (accent color)  
AppColors.onPrimaryColor        // White text on primary

// Background colors
AppColors.pageBackgroundColor   // #F8F9FA (main background)
AppColors.surfaceColor          // #FFFFFF (cards, containers)
AppColors.onSurfaceColor        // #212529 (text on surface)

// Semantic colors
AppColors.errorColor            // #DC3545 (errors)
AppColors.warningColor          // #FFC107 (warnings)
AppColors.successColor          // #28A745 (success states)
AppColors.hintTextColor         // #6C757D (placeholders)

// Utility colors
AppColors.borderColor           // #E0E0E0 (borders)
AppColors.disabledColor         // #9E9E9E (disabled states)
```

### Typography
```dart
// Headings
AppTypography.heading1          // 24px, Bold (main headings)
AppTypography.heading2          // 20px, SemiBold (section headings)
AppTypography.heading3          // 18px, SemiBold (subsection headings)  
AppTypography.heading4          // 16px, Medium (small headings)

// Body text
AppTypography.bodyLarge         // 16px, Regular (main content)
AppTypography.bodyMedium        // 14px, Regular (default text)
AppTypography.bodySmall         // 12px, Regular (small text)

// Labels & special
AppTypography.labelLarge        // 14px, Medium (form labels)
AppTypography.labelMedium       // 12px, Medium (small labels)
AppTypography.buttonLarge       // 16px, Medium (button text)
AppTypography.hint              // 14px, Regular (placeholder text)
AppTypography.error             // 12px, Regular (error messages)
```

### Spacing
```dart
// Standard spacing
AppSpacing.xSmall               // 8px
AppSpacing.small                // 12px  
AppSpacing.medium               // 16px
AppSpacing.large                // 20px
AppSpacing.xLarge               // 24px
AppSpacing.xxLarge              // 32px

// Edge insets
AppSpacing.paddingM             // EdgeInsets.all(16)
AppSpacing.paddingL             // EdgeInsets.all(20)
AppSpacing.paddingHorizontalM   // EdgeInsets.symmetric(horizontal: 16)
AppSpacing.paddingVerticalM     // EdgeInsets.symmetric(vertical: 16)

// Component-specific
AppSpacing.buttonPadding        // EdgeInsets.symmetric(horizontal: 24, vertical: 12)
AppSpacing.textFieldPadding     // EdgeInsets.symmetric(horizontal: 10, vertical: 12)
AppSpacing.cardPadding          // EdgeInsets.all(16)
```

### Border Radius
```dart
AppBorderRadius.small           // 4px
AppBorderRadius.medium          // 8px
AppBorderRadius.large           // 10px
AppBorderRadius.xLarge          // 15px

// Convenience getters
AppBorderRadius.button          // BorderRadius.circular(8)
AppBorderRadius.textField       // BorderRadius.circular(10)
AppBorderRadius.card            // BorderRadius.circular(10)
```

## 🔘 Button Components

### AppCustomRoundedButton (Drop-in replacement for CustomRoundedButton)
```dart
AppCustomRoundedButton(
  buttonTitle: "Sign In",
  onTap: () {
    // Handle tap
  },
  backgroundColor: AppColors.primaryColor,
  titleColor: Colors.white,
  widthPercentage: 0.8,
  showBorder: false,
)

// With border
AppCustomRoundedButton(
  buttonTitle: "Sign Up", 
  onTap: () {},
  backgroundColor: Colors.transparent,
  titleColor: AppColors.primaryColor,
  showBorder: true,
  borderColor: AppColors.primaryColor,
  widthPercentage: 0.8,
)

// With loading state
AppCustomRoundedButton(
  buttonTitle: "Submit",
  onTap: isLoading ? null : _submit,
  backgroundColor: AppColors.primaryColor,
  showBorder: false,
  child: isLoading 
    ? const CircularProgressIndicator()
    : null,
)
```

### AppPrimaryButton (Material Design 3 style)
```dart
AppPrimaryButton(
  text: "Continue",
  onPressed: () {},
  isLoading: false,
)
```

### AppSecondaryButton
```dart
AppSecondaryButton(
  text: "Cancel",
  onPressed: () {},
)
```

### AppTextButton  
```dart
AppTextButton(
  text: "Forgot Password?",
  onPressed: () {},
)
```

## 📝 Text Field Components

### AppTextField
```dart
AppTextField(
  hintText: "Enter your email",
  controller: emailController,
  keyboardType: TextInputType.emailAddress,
)
```

### AppSearchTextField
```dart
AppSearchTextField(
  hintText: "Search students...",
  onChanged: (value) {
    // Handle search
  },
)
```

## 📱 Bottom Sheet Components

### AppModalBottomSheet
```dart
showModalBottomSheet(
  context: context,
  builder: (context) => AppModalBottomSheet(
    title: "Select Option",
    child: Column(
      children: [
        // Your content
      ],
    ),
  ),
);
```

## 🏠 Container Components

### AppCard
```dart
AppCard(
  child: Column(
    children: [
      Text("Card Title", style: AppTypography.heading3),
      Text("Card content", style: AppTypography.bodyMedium),
    ],
  ),
)
```

### AppSurface  
```dart
AppSurface(
  child: Text("Surface content"),
  padding: AppSpacing.paddingM,
)
```

## 🔄 Migration Examples

### Before (Old Style)
```dart
// ❌ Old approach
import 'package:eschool/ui/styles/colors.dart';
import 'package:eschool/ui/widgets/customRoundedButton.dart';

CustomRoundedButton(
  buttonTitle: "Login", 
  onTap: () {},
  backgroundColor: primaryColor,
  titleColor: Colors.white,
  showBorder: false,
  widthPercentage: 0.8,
)

Text(
  "Welcome",
  style: TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.w600,
    color: secondaryColor,
  ),
)
```

### After (Design System)
```dart
// ✅ New approach  
import 'package:eschool/ui/design_system/app_design_system.dart';
import 'package:eschool/ui/design_system/components/app_components.dart';

AppCustomRoundedButton(
  buttonTitle: "Login",
  onTap: () {},
  backgroundColor: AppColors.primaryColor,
  titleColor: Colors.white, 
  showBorder: false,
  widthPercentage: 0.8,
)

Text(
  "Welcome",
  style: AppTypography.heading1,
)
```

## 🎨 Theme Integration

The design system is automatically integrated with the Material Design 3 theme:

```dart
// In your main app (already configured)
MaterialApp(
  theme: AppTheme.lightTheme,          // ✅ Already set up
  darkTheme: AppTheme.darkTheme,       // ✅ Dark theme ready
  themeMode: ThemeMode.system,         // ✅ System theme switching
  // ...
)
```

## 📋 Component Migration Checklist

When migrating a screen, follow this checklist:

### 1. Update Imports
```dart
// ✅ Add design system imports
import 'package:eschool/ui/design_system/app_design_system.dart';
import 'package:eschool/ui/design_system/components/app_components.dart';

// ❌ Remove old imports  
// import 'package:eschool/ui/styles/colors.dart';
// import 'package:eschool/ui/widgets/customRoundedButton.dart';
```

### 2. Replace Components
- [ ] `CustomRoundedButton` → `AppCustomRoundedButton`
- [ ] Hardcoded `TextStyle` → `AppTypography.*`
- [ ] `primaryColor` → `AppColors.primaryColor`
- [ ] `secondaryColor` → `AppColors.secondaryColor`
- [ ] Hardcoded padding → `AppSpacing.*`
- [ ] Hardcoded border radius → `AppBorderRadius.*`

### 3. Test & Verify
- [ ] No compilation errors
- [ ] UI looks consistent with design system
- [ ] Dark theme works properly (if applicable)
- [ ] Interactive elements function correctly

## 🏆 Best Practices

### 1. **Always Use Design Tokens**
```dart
// ✅ Good
Container(
  padding: AppSpacing.paddingM,
  decoration: BoxDecoration(
    color: AppColors.surfaceColor,
    borderRadius: AppBorderRadius.card,
  ),
)

// ❌ Avoid
Container(
  padding: EdgeInsets.all(16),
  decoration: BoxDecoration(
    color: Colors.white,
    borderRadius: BorderRadius.circular(10),
  ),
)
```

### 2. **Use Semantic Color Names**
```dart
// ✅ Good
Container(color: AppColors.errorColor)        // Semantic meaning
Container(color: AppColors.surfaceColor)      // Clear purpose

// ❌ Avoid  
Container(color: Colors.red)                  // Too generic
Container(color: Color(0xFFFFFFFF))          // Hardcoded hex
```

### 3. **Prefer Design System Components**
```dart
// ✅ Good - Use design system button
AppPrimaryButton(text: "Submit", onPressed: () {})

// ❌ Avoid - Custom implementation
ElevatedButton(
  style: ElevatedButton.styleFrom(/* custom styling */),
  onPressed: () {},
  child: Text("Submit"),
)
```

## 🆘 Need Help?

- **Design System Source**: `lib/ui/design_system/`
- **Migration Report**: `DESIGN_SYSTEM_MIGRATION_REPORT.md`
- **Examples**: Check migrated screens in `lib/ui/screens/auth/`

The design system is fully functional and ready to use! Happy coding! 🚀
