import 'package:flutter/material.dart';
import 'package:eschool_teacher/ui/design_system/design_system.dart';
import 'package:eschool_teacher/data/repositories/lesson_booking_repository.dart';
import 'package:eschool_teacher/data/models/lesson_booking.dart';

/// Booked lessons widget displaying today's lessons
/// Matches the design from the provided screenshot
class BookedLessonsWidget extends StatelessWidget {
  const BookedLessonsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section Header
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: AppSpacing.xSmall),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'الحصص المحجوزة',
                style: AppTypography.heading4,
              ),
              AppTextButton(
                onPressed: () {
                  // Navigate to full calendar
                },
                text: 'عرض الكل',
                fontSize: 12,
              ),
            ],
          ),
        ),

        const SizedBox(height: AppSpacing.small),

        // Lessons List
        FutureBuilder<List<LessonBooking>>(
          future: LessonBookingRepository().getTodaysLessons(),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return _buildLoadingLessons();
            }

            if (snapshot.hasError) {
              return _buildErrorState();
            }

            final lessons = snapshot.data ?? [];

            if (lessons.isEmpty) {
              return _buildEmptyState();
            }

            return Column(
              children: lessons.map((lesson) {
                return _buildLessonCard(context, lesson);
              }).toList(),
            );
          },
        ),
      ],
    );
  }

  Widget _buildLessonCard(BuildContext context, LessonBooking lesson) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(bottom: AppSpacing.xSmall),
      padding: AppSpacing.lessonCardPadding,
      decoration: BoxDecoration(
        color: AppColors.cardBackgroundColor,
        borderRadius: AppBorderRadius.lessonCard,
        boxShadow: AppShadows.small,
        border: Border.all(
          color: _getLessonStatusColor(lesson.status).withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // Time and Date
          Container(
            padding: const EdgeInsets.all(AppSpacing.small),
            decoration: BoxDecoration(
              color: _getLessonStatusColor(lesson.status).withValues(alpha: 0.1),
              borderRadius: AppBorderRadius.roundedMedium,
            ),
            child: Column(
              children: [
                Icon(
                  Icons.schedule,
                  size: AppComponents.iconSmall,
                  color: _getLessonStatusColor(lesson.status),
                ),
                const SizedBox(height: AppSpacing.xxSmall),
                Text(
                  lesson.formattedTime,
                  style: AppTypography.lessonTime.copyWith(
                    color: _getLessonStatusColor(lesson.status),
                    fontSize: 10,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(width: AppSpacing.medium),

          // Lesson Info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Date
                Text(
                  lesson.formattedDate,
                  style: AppTypography.labelMedium.copyWith(
                    color: AppColors.onSurfaceColor,
                  ),
                ),

                const SizedBox(height: AppSpacing.xxSmall),

                // Subject and Level
                Text(
                  '${lesson.subject.title} - ${lesson.educationStage?.name ?? 'عام'}',
                  style: AppTypography.bodyMedium.copyWith(
                    fontWeight: FontWeight.medium,
                  ),
                ),

                const SizedBox(height: AppSpacing.xxSmall),

                // Lesson Type and Students
                Row(
                  children: [
                    Icon(
                      lesson.lessonType == LessonType.individual
                          ? Icons.person
                          : Icons.group,
                      size: AppComponents.iconXSmall,
                      color: AppColors.onSurfaceColor,
                    ),
                    const SizedBox(width: AppSpacing.xxSmall),
                    Text(
                      lesson.lessonType == LessonType.individual
                          ? 'درس فردي'
                          : 'درس جماعي (${lesson.studentCount} طلاب)',
                      style: AppTypography.labelSmall.copyWith(
                        color: AppColors.onSurfaceColor,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Status and Actions
          Column(
            children: [
              // Status Badge
              AppStatusBadge(
                text: _getLessonStatusText(lesson.status),
                color: _getLessonStatusColor(lesson.status),
              ),

              const SizedBox(height: AppSpacing.xSmall),

              // Action Button
              if (lesson.status == LessonStatus.scheduled && lesson.zoomLink != null)
                AppIconButton(
                  onPressed: () {
                    // Launch Zoom link
                    _launchZoomMeeting(lesson.zoomLink!);
                  },
                  icon: Icons.videocam,
                  backgroundColor: AppColors.successColor,
                  iconColor: AppColors.onPrimaryColor,
                  size: AppComponents.iconSmall,
                  padding: AppSpacing.xSmall,
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingLessons() {
    return Column(
      children: List.generate(2, (index) {
        return Container(
          width: double.infinity,
          height: AppComponents.lessonCardHeight,
          margin: const EdgeInsets.only(bottom: AppSpacing.xSmall),
          decoration: BoxDecoration(
            color: AppColors.shimmerBaseColor,
            borderRadius: AppBorderRadius.lessonCard,
          ),
        );
      }),
    );
  }

  Widget _buildErrorState() {
    return AppErrorContainer(
      message: 'حدث خطأ في تحميل الحصص',
      onRetry: () {
        // Retry loading lessons
      },
    );
  }

  Widget _buildEmptyState() {
    return AppNoDataContainer(
      message: 'لا توجد حصص محجوزة اليوم',
      icon: Icons.event_available,
    );
  }

  Color _getLessonStatusColor(LessonStatus status) {
    switch (status) {
      case LessonStatus.scheduled:
        return AppColors.infoColor;
      case LessonStatus.ongoing:
        return AppColors.successColor;
      case LessonStatus.completed:
        return AppColors.successColor;
      case LessonStatus.cancelled:
        return AppColors.errorColor;
    }
  }

  String _getLessonStatusText(LessonStatus status) {
    switch (status) {
      case LessonStatus.scheduled:
        return 'مجدول';
      case LessonStatus.ongoing:
        return 'جاري';
      case LessonStatus.completed:
        return 'مكتمل';
      case LessonStatus.cancelled:
        return 'ملغي';
    }
  }

  void _launchZoomMeeting(String zoomLink) {
    // TODO: Implement Zoom link launching
    // This would typically use url_launcher package
    print('Launching Zoom meeting: $zoomLink');
  }
}
