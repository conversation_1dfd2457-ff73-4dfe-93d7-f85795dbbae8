import 'package:eschool/cubits/feesReceiptCubit.dart';
import 'package:eschool/data/models/student.dart';
import 'package:eschool/ui/design_system/app_design_system.dart';
import 'package:eschool/ui/design_system/components/app_components.dart';
import 'package:eschool/ui/widgets/customCircularProgressIndicator.dart';
import 'package:eschool/utils/labelKeys.dart';
import 'package:eschool/utils/uiUtils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:open_filex/open_filex.dart';

class StudentDownloadFeePaidReceiptButton extends StatelessWidget {
  final Student studentDetails;
  const StudentDownloadFeePaidReceiptButton(
      {super.key, required this.studentDetails,});

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<FeesReceiptCubit, FeesReceiptState>(
      builder: (context, state) {
        return AppCustomRoundedButton(
          onTap: () {
            if (context.read<FeesReceiptCubit>().state
                is FeesReceiptDownloadInProgress) {
              return;
            } else {
              context.read<FeesReceiptCubit>().downloadFeesReceipt(
                    fileNamePrefix:
                        "${studentDetails.name}_${classKey}_${studentDetails.classSectionName}",
                  );
            }
          },
          widthPercentage: 0.7,
          height: 50,
          textAlign: TextAlign.center,
          backgroundColor: AppColors.pageBackgroundColor,
          buttonTitle:
              UiUtils.getTranslatedLabel(context, downloadFeesReceiptKey),
          titleColor: AppColors.primaryColor,
          showBorder: true,
          borderColor: AppColors.primaryColor,
          child: state is FeesReceiptDownloadInProgress
              ? CustomCircularProgressIndicator(
                  strokeWidth: 2,
                  widthAndHeight: 20,
                  indicatorColor: AppColors.primaryColor,
                )
              : null,
        );
      },
      listener: (context, state) async {
        if (state is FeesReceiptDownloadSuccess) {
          final String msg =
              "${UiUtils.getTranslatedLabel(context, state.successMessageKey)}\n${studentDetails.name},$classKey ${studentDetails.classSectionName}";
          UiUtils.showCustomSnackBar(
            context: context,
            errorMessage: msg,
            backgroundColor: Theme.of(context).colorScheme.primary,
          );
          try {
            final fileOpenResult = await OpenFilex.open(state.filePath);
            if (fileOpenResult.type != ResultType.done) {
              UiUtils.showCustomSnackBar(
                context: context,
                errorMessage: UiUtils.getTranslatedLabel(
                  context,
                  unableToOpenKey,
                ),
                backgroundColor: Theme.of(context).colorScheme.error,
              );
            }
          } catch (_) {
            if (context.mounted) {
              UiUtils.showCustomSnackBar(
                  context: context,
                  errorMessage:
                      UiUtils.getTranslatedLabel(context, unableToOpenKey),
                  backgroundColor: AppColors.errorColor,);
            }
          }
        }
        if (state is FeesReceiptDownloadFailure) {
          UiUtils.showCustomSnackBar(
            context: context,
            errorMessage: UiUtils.getErrorMessageFromErrorCode(
              context,
              state.errorMessage,
            ),
            backgroundColor: Theme.of(context).colorScheme.error,
          );
        }
      },
    );
  }
}
