import 'package:flutter/material.dart';
import 'package:eschool_teacher/ui/design_system/design_system.dart';

/// Announcement banner widget displaying important notifications
/// Matches the design from the provided screenshot
class AnnouncementBannerWidget extends StatelessWidget {
  const AnnouncementBannerWidget({super.key});

  @override
  Widget build(BuildContext context) {
    // Dummy announcement data
    final announcements = [
      {
        'title': 'إعلان هام',
        'message': 'خبر الإعلان بخصوص تحديث نظام الدروس الإلكترونية',
        'type': 'important',
        'date': DateTime.now(),
      },
      {
        'title': 'تحديث جديد',
        'message': 'تم إضافة ميزات جديدة لتحسين تجربة التدريس',
        'type': 'info',
        'date': DateTime.now().subtract(const Duration(days: 1)),
      },
    ];

    if (announcements.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.symmetric(vertical: AppSpacing.xSmall),
      child: Column(
        children: announcements.map((announcement) {
          return _buildAnnouncementCard(context, announcement);
        }).toList(),
      ),
    );
  }

  Widget _buildAnnouncementCard(BuildContext context, Map<String, dynamic> announcement) {
    final type = announcement['type'] as String;
    final isImportant = type == 'important';

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(bottom: AppSpacing.xSmall),
      padding: const EdgeInsets.all(AppSpacing.medium),
      decoration: BoxDecoration(
        color: isImportant
            ? AppColors.warningColor.withValues(alpha: 0.1)
            : AppColors.infoColor.withValues(alpha: 0.1),
        borderRadius: AppBorderRadius.roundedLarge,
        border: Border.all(
          color: isImportant
              ? AppColors.warningColor.withValues(alpha: 0.3)
              : AppColors.infoColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Icon
          Container(
            padding: const EdgeInsets.all(AppSpacing.xSmall),
            decoration: BoxDecoration(
              color: isImportant ? AppColors.warningColor : AppColors.infoColor,
              shape: BoxShape.circle,
            ),
            child: Icon(
              isImportant ? Icons.priority_high : Icons.info_outline,
              size: AppComponents.iconSmall,
              color: AppColors.onPrimaryColor,
            ),
          ),

          const SizedBox(width: AppSpacing.medium),

          // Content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Title
                Text(
                  announcement['title'] as String,
                  style: AppTypography.labelLarge.copyWith(
                    color: isImportant ? AppColors.warningColor : AppColors.infoColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),

                const SizedBox(height: AppSpacing.xxSmall),

                // Message
                Text(
                  announcement['message'] as String,
                  style: AppTypography.bodySmall.copyWith(
                    color: AppColors.arabicTextColor,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),

                const SizedBox(height: AppSpacing.xSmall),

                // Date
                Text(
                  _formatDate(announcement['date'] as DateTime),
                  style: AppTypography.caption.copyWith(
                    color: AppColors.onSurfaceColor,
                  ),
                ),
              ],
            ),
          ),

          // Close button
          AppIconButton(
            onPressed: () {
              // Handle dismiss announcement
            },
            icon: Icons.close,
            size: AppComponents.iconXSmall,
            iconColor: AppColors.onSurfaceColor,
            padding: AppSpacing.xxSmall,
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'اليوم';
    } else if (difference.inDays == 1) {
      return 'أمس';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} أيام';
    } else {
      final months = [
        'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
        'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
      ];
      return '${date.day} ${months[date.month - 1]}';
    }
  }
}
