import 'package:flutter/material.dart';
import '../app_design_system.dart';

/// Reusable button components for the teacher app
/// 
/// This library provides consistent button styling adapted for the teacher app
/// with Arabic RTL support and teacher-specific use cases.

/// Primary button component for main actions
class AppPrimaryButton extends StatelessWidget {
  const AppPrimaryButton({
    super.key,
    required this.onPressed,
    required this.text,
    this.isLoading = false,
    this.isEnabled = true,
    this.width,
    this.height,
    this.icon,
  });

  final VoidCallback? onPressed;
  final String text;
  final bool isLoading;
  final bool isEnabled;
  final double? width;
  final double? height;
  final Widget? icon;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width,
      height: height ?? AppComponents.buttonHeight,
      child: ElevatedButton.icon(
        onPressed: isEnabled && !isLoading ? onPressed : null,
        icon: isLoading 
            ? SizedBox(
                width: AppComponents.iconSmall,
                height: AppComponents.iconSmall,
                child: const CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.onPrimaryColor),
                ),
              )
            : icon ?? const SizedBox.shrink(),
        label: Text(
          text,
          style: AppTypography.buttonMedium,
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primaryColor,
          foregroundColor: AppColors.onPrimaryColor,
          disabledBackgroundColor: AppColors.disabledColor,
          disabledForegroundColor: AppColors.onSurfaceColor,
          elevation: 2,
          shadowColor: AppColors.primaryColor.withOpacity(0.3),
          shape: RoundedRectangleBorder(
            borderRadius: AppBorderRadius.button,
          ),
          padding: AppSpacing.buttonPadding,
        ),
      ),
    );
  }
}

/// Secondary button component with outline style
class AppSecondaryButton extends StatelessWidget {
  const AppSecondaryButton({
    super.key,
    required this.onPressed,
    required this.text,
    this.isLoading = false,
    this.isEnabled = true,
    this.width,
    this.height,
    this.icon,
  });

  final VoidCallback? onPressed;
  final String text;
  final bool isLoading;
  final bool isEnabled;
  final double? width;
  final double? height;
  final Widget? icon;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width,
      height: height ?? AppComponents.buttonHeight,
      child: OutlinedButton.icon(
        onPressed: isEnabled && !isLoading ? onPressed : null,
        icon: isLoading 
            ? SizedBox(
                width: AppComponents.iconSmall,
                height: AppComponents.iconSmall,
                child: const CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.primaryColor),
                ),
              )
            : icon ?? const SizedBox.shrink(),
        label: Text(
          text,
          style: AppTypography.buttonMedium.copyWith(
            color: AppColors.primaryColor,
          ),
        ),
        style: OutlinedButton.styleFrom(
          foregroundColor: AppColors.primaryColor,
          disabledForegroundColor: AppColors.disabledColor,
          side: const BorderSide(
            color: AppColors.primaryColor,
            width: 1.5,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: AppBorderRadius.button,
          ),
          padding: AppSpacing.buttonPadding,
        ),
      ),
    );
  }
}

/// Custom rounded button for teacher-specific actions
class AppCustomRoundedButton extends StatelessWidget {
  final String? buttonTitle;
  final double? height;
  final double? widthPercentage;
  final AlignmentGeometry? alignment;
  final EdgeInsets? padding;
  final Function? onTap;
  final Color backgroundColor;
  final TextAlign? textAlign;
  final double? radius;
  final Color? shadowColor;
  final bool showBorder;
  final Color? borderColor;
  final Color? titleColor;
  final double? textSize;
  final FontWeight? fontWeight;
  final double? elevation;
  final Widget? child;

  const AppCustomRoundedButton({
    super.key,
    this.widthPercentage,
    required this.backgroundColor,
    this.textAlign,
    this.borderColor,
    this.elevation,
    required this.buttonTitle,
    this.onTap,
    this.radius,
    this.shadowColor,
    this.child,
    required this.showBorder,
    this.height = 55,
    this.titleColor,
    this.fontWeight,
    this.textSize,
    this.alignment = Alignment.center,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      shadowColor: shadowColor ?? Colors.black54,
      elevation: elevation ?? 0.0,
      color: backgroundColor,
      borderRadius: BorderRadius.circular(radius ?? AppBorderRadius.large),
      child: InkWell(
        borderRadius: BorderRadius.circular(radius ?? AppBorderRadius.large),
        onTap: onTap as void Function()?,
        child: Container(
          padding: padding ?? AppSpacing.paddingM,
          alignment: alignment,
          height: height,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(radius ?? AppBorderRadius.large),
            border: showBorder
                ? Border.all(
                    color: borderColor ?? AppColors.borderColor,
                  )
                : null,
          ),
          width: widthPercentage != null
              ? MediaQuery.of(context).size.width * widthPercentage!
              : null,
          child: child ??
              Text(
                "$buttonTitle",
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                textAlign: textAlign ?? TextAlign.center,
                style: AppTypography.bodyMedium.copyWith(
                  fontSize: textSize ?? 14.0,
                  color: titleColor ?? AppColors.onPrimaryColor,
                  fontWeight: fontWeight ?? FontWeight.normal,
                ),
              ),
        ),
      ),
    );
  }
}

/// Close button for dialogs and bottom sheets
class AppCloseButton extends StatelessWidget {
  const AppCloseButton({
    super.key,
    required this.onPressed,
    this.size = AppComponents.iconMedium,
    this.backgroundColor = AppColors.surfaceColor,
    this.iconColor = AppColors.secondaryColor,
    this.hasBorder = true,
  });

  final VoidCallback onPressed;
  final double size;
  final Color backgroundColor;
  final Color iconColor;
  final bool hasBorder;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size + 16,
      height: size + 16,
      decoration: BoxDecoration(
        color: backgroundColor,
        shape: BoxShape.circle,
        border: hasBorder
            ? Border.all(
                color: AppColors.borderColor,
                width: 1,
              )
            : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(size),
          child: Icon(
            Icons.close,
            size: size,
            color: iconColor,
          ),
        ),
      ),
    );
  }
}

/// Text button for less prominent actions
class AppTextButton extends StatelessWidget {
  const AppTextButton({
    super.key,
    required this.onPressed,
    required this.text,
    this.textColor = AppColors.primaryColor,
    this.isEnabled = true,
    this.fontSize,
  });

  final VoidCallback? onPressed;
  final String text;
  final Color textColor;
  final bool isEnabled;
  final double? fontSize;

  @override
  Widget build(BuildContext context) {
    return TextButton(
      onPressed: isEnabled ? onPressed : null,
      style: TextButton.styleFrom(
        foregroundColor: textColor,
        disabledForegroundColor: AppColors.disabledColor,
        padding: const EdgeInsets.symmetric(
          horizontal: AppSpacing.medium,
          vertical: AppSpacing.xSmall,
        ),
      ),
      child: Text(
        text,
        style: AppTypography.buttonMedium.copyWith(
          color: isEnabled ? textColor : AppColors.disabledColor,
          fontSize: fontSize,
        ),
      ),
    );
  }
}

/// Icon button with consistent styling
class AppIconButton extends StatelessWidget {
  const AppIconButton({
    super.key,
    required this.onPressed,
    required this.icon,
    this.backgroundColor = Colors.transparent,
    this.iconColor = AppColors.secondaryColor,
    this.size = AppComponents.iconMedium,
    this.padding = AppSpacing.xSmall,
    this.borderRadius,
    this.isEnabled = true,
  });

  final VoidCallback? onPressed;
  final IconData icon;
  final Color backgroundColor;
  final Color iconColor;
  final double size;
  final double padding;
  final BorderRadius? borderRadius;
  final bool isEnabled;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: borderRadius ?? BorderRadius.circular(padding),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: isEnabled ? onPressed : null,
          borderRadius: borderRadius ?? BorderRadius.circular(padding),
          child: Padding(
            padding: EdgeInsets.all(padding),
            child: Icon(
              icon,
              size: size,
              color: isEnabled ? iconColor : AppColors.disabledColor,
            ),
          ),
        ),
      ),
    );
  }
}
