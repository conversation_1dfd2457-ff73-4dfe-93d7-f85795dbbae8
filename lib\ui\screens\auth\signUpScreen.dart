import 'package:eschool/app/routes.dart';
import 'package:eschool/cubits/auth_cubit.dart';
import 'package:eschool/cubits/signUpCubit.dart';
import 'package:eschool/data/repositories/authRepository.dart';
import 'package:eschool/ui/design_system/app_design_system.dart';
import 'package:eschool/ui/design_system/components/app_components.dart';
import 'package:eschool/ui/screens/auth/widgets/verificationCodeBottomsheet.dart';
import 'package:eschool/ui/widgets/customCircularProgressIndicator.dart';
import 'package:eschool/ui/widgets/customTextFieldContainer.dart';
import 'package:eschool/ui/widgets/passwordHideShowButton.dart';
import 'package:eschool/utils/labelKeys.dart';
import 'package:eschool/utils/uiUtils.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:eschool/ui/widgets/bottomsheetTopBarMenu.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_sign_in/google_sign_in.dart';

class SignUpScreen extends StatefulWidget {
  const SignUpScreen({super.key});

  @override
  State<SignUpScreen> createState() => _SignUpScreenState();

  static Route route(RouteSettings routeSettings) {
    return CupertinoPageRoute(
      builder: (context) => MultiBlocProvider(
        providers: [
          BlocProvider<SignUpCubit>(
            create: (_) => SignUpCubit(AuthRepository()),
          ),
          BlocProvider.value(
            value: context.read<AuthCubit>(),
          ),
        ],
        child: const SignUpScreen(),
      ),
    );
  }
}

class _SignUpScreenState extends State<SignUpScreen> with TickerProviderStateMixin {
  late final AnimationController _animationController = AnimationController(
    vsync: this,
    duration: const Duration(milliseconds: 1000),
  );

  late final Animation<double> _formAnimation =
      Tween<double>(begin: 0.0, end: 1.0).animate(
    CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.5, 1.0, curve: Curves.easeInOut),
    ),
  );

  final TextEditingController _firstNameTextEditingController = TextEditingController();
  final TextEditingController _lastNameTextEditingController = TextEditingController();
  final TextEditingController _emailTextEditingController = TextEditingController();
  final TextEditingController _passwordTextEditingController = TextEditingController();
  final TextEditingController _confirmPasswordTextEditingController = TextEditingController();
  final TextEditingController _phoneNumberTextEditingController = TextEditingController();

  bool _hidePassword = true;
  bool _hideConfirmPassword = true;

  @override
  void initState() {
    super.initState();
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _firstNameTextEditingController.dispose();
    _lastNameTextEditingController.dispose();
    _emailTextEditingController.dispose();
    _passwordTextEditingController.dispose();
    _confirmPasswordTextEditingController.dispose();
    _phoneNumberTextEditingController.dispose();
    super.dispose();
  }

  void _signUp() {
    if (_firstNameTextEditingController.text.trim().isEmpty) {
      UiUtils.showCustomSnackBar(
        context: context,
        errorMessage: UiUtils.getTranslatedLabel(context, pleaseEnterFirstNameKey),
        backgroundColor: Theme.of(context).colorScheme.error,
      );
      return;
    }

    if (_lastNameTextEditingController.text.trim().isEmpty) {
      UiUtils.showCustomSnackBar(
        context: context,
        errorMessage: UiUtils.getTranslatedLabel(context, pleaseEnterLastNameKey),
        backgroundColor: Theme.of(context).colorScheme.error,
      );
      return;
    }

    if (_emailTextEditingController.text.trim().isEmpty) {
      UiUtils.showCustomSnackBar(
        context: context,
        errorMessage: UiUtils.getTranslatedLabel(context, pleaseEnterEmailKey),
        backgroundColor: Theme.of(context).colorScheme.error,
      );
      return;
    }

    if (_passwordTextEditingController.text.trim().isEmpty) {
      UiUtils.showCustomSnackBar(
        context: context,
        errorMessage: UiUtils.getTranslatedLabel(context, pleaseEnterPasswordKey),
        backgroundColor: Theme.of(context).colorScheme.error,
      );
      return;
    }

    if (_confirmPasswordTextEditingController.text.trim().isEmpty) {
      UiUtils.showCustomSnackBar(
        context: context,
        errorMessage: UiUtils.getTranslatedLabel(context, pleaseConfirmPasswordKey),
        backgroundColor: Theme.of(context).colorScheme.error,
      );
      return;
    }

    if (_passwordTextEditingController.text != _confirmPasswordTextEditingController.text) {
      UiUtils.showCustomSnackBar(
        context: context,
        errorMessage: UiUtils.getTranslatedLabel(context, passwordsDoNotMatchKey),
        backgroundColor: Theme.of(context).colorScheme.error,
      );
      return;
    }

    if (_phoneNumberTextEditingController.text.trim().isEmpty) {
      UiUtils.showCustomSnackBar(
        context: context,
        errorMessage: UiUtils.getTranslatedLabel(context, pleaseEnterPhoneNumberKey),
        backgroundColor: Theme.of(context).colorScheme.error,
      );
      return;
    }

    context.read<SignUpCubit>().signUpStudent(
          firstName: _firstNameTextEditingController.text.trim(),
          lastName: _lastNameTextEditingController.text.trim(),
          email: _emailTextEditingController.text.trim(),
          password: _passwordTextEditingController.text.trim(),
          phoneNumber: _phoneNumberTextEditingController.text.trim(),
        );
  }

  // Handle sign up process based on state changes
  void _handleSignUpState(BuildContext context, SignUpState state) {
    if (state is SignUpSuccess) {
      // Authenticate the user
      context.read<AuthCubit>().authenticateUser(
            jwtToken: state.jwtToken,
            student: state.student,
          );

      // Navigate to home screen
      Navigator.of(context).pushNamedAndRemoveUntil(
        Routes.home,
        (Route<dynamic> route) => false,
      );
    } else if (state is SignUpFailure) {
      UiUtils.showCustomSnackBar(
        context: context,
        errorMessage: state.errorMessage,
        backgroundColor: Theme.of(context).colorScheme.error,
      );
    } else if (state is EmailVerificationSuccess) {
      // Email verified successfully but auto-login failed (e.g., after hot reload)
      UiUtils.showCustomSnackBar(
        context: context,
        errorMessage: "Email verified successfully! Please sign in with your credentials.",
        backgroundColor: Theme.of(context).colorScheme.primary,
      );
      
      // Navigate to login screen
      Navigator.of(context).pushReplacementNamed(Routes.login);
    } else if (state is RegisterVerificationNeeded) {
      // Get the cubit before showing the bottom sheet
      final signUpCubit = context.read<SignUpCubit>();

      // Show verification code input
      UiUtils.showBottomSheet(
        child: BlocProvider<SignUpCubit>.value(
          value: signUpCubit, // Provide the existing SignUpCubit instance
          child: VerificationCodeBottomsheet(
            email: state.email,
            titleKey: "verifyYourEmailKey",
            descriptionKey: "verificationCodeSendToKey",
            verificationType: VerificationType.registration,
            onVerify: (code) {
              // Cubit's verifyEmail will be called from within VerificationCodeBottomsheet
            },
            onResendCode: () {
              // Cubit's resendVerificationCode will be called from within VerificationCodeBottomsheet
            },
          ),
        ),
        context: context,
      );
    }
  }

  Widget _buildOnboardingImage() {
    return Align(
      alignment: Alignment.topCenter,
      child: Container(
        width: MediaQuery.of(context).size.width,
        margin: EdgeInsets.only(
          top: MediaQuery.of(context).padding.top +
              MediaQuery.of(context).size.height * (0.02),
        ),
        height: MediaQuery.of(context).size.height * (0.22),
        child: SvgPicture.asset(
          "assets/images/onboarding.svg",
          fit: BoxFit.contain,
        ),
      ),
    );
  }

  Widget _buildGoogleSignUpButton() {
    return Center(
      child: BlocConsumer<SignUpCubit, SignUpState>(
        listener: (context, state) {
          // Google sign-up is handled by the same listener as regular sign-up
          // in the build method below
        },
        builder: (context, state) {
          return CustomRoundedButton(
            onTap: () {
              if (state is SignUpInProgress) {
                return;
              }

              FocusScope.of(context).unfocus();
              context.read<SignUpCubit>().signUpWithGoogle();
            },
            widthPercentage: 0.8,
            backgroundColor: Colors.transparent,
            buttonTitle: UiUtils.getTranslatedLabel(context, "signUpWithGoogle"),
            titleColor: Theme.of(context).colorScheme.primary,
            showBorder: true,
            borderColor: Theme.of(context).colorScheme.primary,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(
                  'assets/images/google_logo.png',
                  height: 24,
                  width: 24,
                ),
                const SizedBox(width: 10),
                Text(
                  UiUtils.getTranslatedLabel(context, "signUpWithGoogle"),
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildSignUpForm() {
    return Align(
      alignment: Alignment.bottomCenter,
      child: FadeTransition(
        opacity: _formAnimation,
        child: Container(
          // Remove fixed margin and height, use constraints instead
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.8,
          ),
          decoration: BoxDecoration(
            color: Theme.of(context).scaffoldBackgroundColor,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(30.0),
              topRight: Radius.circular(30.0),
            ),
          ),
          child: Padding(
            padding: EdgeInsets.only(
              bottom: MediaQuery.of(context).viewInsets.bottom,
            ),
            child: NotificationListener(
              onNotification: (OverscrollIndicatorNotification overscroll) {
                overscroll.disallowIndicator();
                return true;
              },
              child: SingleChildScrollView(
                physics: const ClampingScrollPhysics(),
                padding: EdgeInsets.only(
                  left: MediaQuery.of(context).size.width * (0.075),
                  right: MediaQuery.of(context).size.width * (0.075),
                  top: MediaQuery.of(context).size.height * (0.05),
                  bottom: MediaQuery.of(context).size.height * (0.05),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      UiUtils.getTranslatedLabel(context, signUpKey),
                      style: TextStyle(
                        fontSize: 28.0,
                        fontWeight: FontWeight.bold,
                        color: UiUtils.getColorScheme(context).secondary,
                      ),
                    ),
                    const SizedBox(
                      height: 5.0,
                    ),
                    Text(
                      UiUtils.getTranslatedLabel(context, createAccountKey),
                      style: TextStyle(
                        fontSize: 18.0,
                        height: 1.2,
                        color: UiUtils.getColorScheme(context).secondary,
                      ),
                    ),
                    const SizedBox(
                      height: 15.0,
                    ),
                    // First name and last name fields side by side
                    Row(
                      children: [
                        // First name field (half width)
                        Expanded(
                          child: CustomTextFieldContainer(
                            hideText: false,
                            hintTextKey: firstNameKey,
                            bottomPadding: 0,
                            textEditingController: _firstNameTextEditingController,
                            suffixWidget: Padding(
                              padding: const EdgeInsets.all(12.0),
                              child: SvgPicture.asset(
                                UiUtils.getImagePath("user_icon.svg"),
                                colorFilter: ColorFilter.mode(
                                  UiUtils.getColorScheme(context).secondary,
                                  BlendMode.srcIn,
                                ),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 10.0),
                        // Last name field (half width)
                        Expanded(
                          child: CustomTextFieldContainer(
                            hideText: false,
                            hintTextKey: lastNameKey,
                            bottomPadding: 0,
                            textEditingController: _lastNameTextEditingController,
                            suffixWidget: Padding(
                              padding: const EdgeInsets.all(12.0),
                              child: SvgPicture.asset(
                                UiUtils.getImagePath("user_icon.svg"),
                                colorFilter: ColorFilter.mode(
                                  UiUtils.getColorScheme(context).secondary,
                                  BlendMode.srcIn,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 10.0,
                    ),
                    // Email field
                    CustomTextFieldContainer(
                      hideText: false,
                      hintTextKey: emailKey,
                      bottomPadding: 0,
                      textEditingController: _emailTextEditingController,
                      suffixWidget: Padding(
                        padding: const EdgeInsets.all(12.0),
                        child: SvgPicture.asset(
                          UiUtils.getImagePath("mail_icon.svg"),
                          colorFilter: ColorFilter.mode(
                            UiUtils.getColorScheme(context).secondary,
                            BlendMode.srcIn,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(
                      height: 10.0,
                    ),
                    // Password field
                    CustomTextFieldContainer(
                      textEditingController: _passwordTextEditingController,
                      suffixWidget: PasswordHideShowButton(
                        hidePassword: _hidePassword,
                        onTap: () {
                          setState(() {
                            _hidePassword = !_hidePassword;
                          });
                        },
                      ),
                      hideText: _hidePassword,
                      hintTextKey: passwordKey,
                      bottomPadding: 0,
                    ),
                    const SizedBox(
                      height: 10.0,
                    ),
                    // Confirm password field
                    CustomTextFieldContainer(
                      textEditingController: _confirmPasswordTextEditingController,
                      suffixWidget: PasswordHideShowButton(
                        hidePassword: _hideConfirmPassword,
                        onTap: () {
                          setState(() {
                            _hideConfirmPassword = !_hideConfirmPassword;
                          });
                        },
                      ),
                      hideText: _hideConfirmPassword,
                      hintTextKey: confirmPasswordKey,
                      bottomPadding: 0,
                    ),
                    const SizedBox(
                      height: 10.0,
                    ),
                    // Phone number field
                    CustomTextFieldContainer(
                      hideText: false,
                      hintTextKey: phoneNumberKey,
                      bottomPadding: 0,
                      keyboardType: TextInputType.phone,
                      textEditingController: _phoneNumberTextEditingController,
                    ),
                    const SizedBox(
                      height: 15.0,
                    ),
                    // Sign up button
                    Center(
                      child: BlocConsumer<SignUpCubit, SignUpState>(
                        listener: (context, state) {
                          _handleSignUpState(context, state);
                        },
                        builder: (context, state) {
                          return CustomRoundedButton(
                            onTap: () {
                              if (state is SignUpInProgress) {
                                return;
                              }
                              FocusScope.of(context).unfocus();
                              _signUp();
                            },
                            widthPercentage: 0.8,
                            backgroundColor: UiUtils.getColorScheme(context).primary,
                            buttonTitle: UiUtils.getTranslatedLabel(context, signUpKey),
                            titleColor: Theme.of(context).scaffoldBackgroundColor,
                            showBorder: false,
                            child: state is SignUpInProgress
                                ? const CustomCircularProgressIndicator(
                                    strokeWidth: 2,
                                    widthAndHeight: 20,
                                  )
                                : null,
                          );
                        },
                      ),
                    ),
                    const SizedBox(
                      height: 10.0,
                    ),
                    _buildGoogleSignUpButton(), // Add Google Sign-Up button directly below
                    const SizedBox(
                      height: 20,
                    ),
                    // Already have an account? Login
                    Center(
                      child: InkWell(
                        onTap: () {
                          Navigator.of(context).pushReplacementNamed(Routes.studentLogin);
                        },
                        child: RichText(
                          text: TextSpan(
                            children: [
                              TextSpan(
                                style: TextStyle(
                                  fontSize: 14.0,
                                  color: UiUtils.getColorScheme(context).primary,
                                ),
                                text: UiUtils.getTranslatedLabel(context, alreadyHaveAccountKey),
                              ),
                              TextSpan(
                                style: TextStyle(
                                  fontWeight: FontWeight.w500,
                                  fontSize: 14.0,
                                  color: UiUtils.getColorScheme(context).secondary,
                                ),
                                text: " ${UiUtils.getTranslatedLabel(context, signInKey)}",
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: primaryColor,
      resizeToAvoidBottomInset: false,
      body: Stack(
        children: [
          _buildOnboardingImage(),
          _buildSignUpForm(),
        ],
      ),
    );
  }
}
