import 'package:flutter/material.dart';
import 'package:eschool_teacher/ui/design_system/design_system.dart';
import 'package:eschool_teacher/app/routes.dart';

/// Assignments grid widget displaying assignment cards
/// Matches the design from the provided screenshot
class AssignmentsGridWidget extends StatelessWidget {
  const AssignmentsGridWidget({super.key});

  @override
  Widget build(BuildContext context) {
    // Dummy assignment data
    final assignments = [
      {
        'id': '1',
        'title': 'الوحدة الأولى',
        'subject': 'اللغة العربية',
        'icon': Icons.book,
        'color': AppColors.primaryColor,
        'submissionsCount': 5,
      },
      {
        'id': '2',
        'title': 'التمارين الرياضية',
        'subject': 'الرياضيات',
        'icon': Icons.calculate,
        'color': AppColors.successColor,
        'submissionsCount': 3,
      },
      {
        'id': '3',
        'title': 'قوانين نيوتن',
        'subject': 'الفيزياء',
        'icon': Icons.science,
        'color': AppColors.infoColor,
        'submissionsCount': 7,
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section Header
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: AppSpacing.xSmall),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'الواجبات',
                style: AppTypography.heading4,
              ),
              AppTextButton(
                onPressed: () {
                  Navigator.of(context).pushNamed(Routes.assignments);
                },
                text: 'عرض الكل',
                fontSize: 12,
              ),
            ],
          ),
        ),

        const SizedBox(height: AppSpacing.small),

        // Assignments Grid
        SizedBox(
          height: 120,
          child: ListView(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: AppSpacing.xSmall),
            children: [
              // Add Assignment Card
              _buildAddAssignmentCard(context),

              const SizedBox(width: AppSpacing.small),

              // Assignment Cards
              ...assignments.map((assignment) {
                return Padding(
                  padding: const EdgeInsets.only(right: AppSpacing.small),
                  child: _buildAssignmentCard(context, assignment),
                );
              }),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAddAssignmentCard(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Navigator.of(context).pushNamed(
          Routes.addAssignment,
          arguments: {
            'editAssignment': false,
            'assignment': null,
          },
        );
      },
      child: Container(
        width: 100,
        height: 100,
        decoration: BoxDecoration(
          color: AppColors.surfaceColor,
          borderRadius: AppBorderRadius.assignmentCard,
          border: Border.all(
            color: AppColors.primaryColor,
            width: 2,
            style: BorderStyle.solid,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(AppSpacing.xSmall),
              decoration: BoxDecoration(
                color: AppColors.primaryColor,
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.add,
                size: AppComponents.iconMedium,
                color: AppColors.onPrimaryColor,
              ),
            ),
            const SizedBox(height: AppSpacing.xSmall),
            Text(
              'إضافة\nواجب',
              style: AppTypography.labelSmall.copyWith(
                color: AppColors.primaryColor,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAssignmentCard(BuildContext context, Map<String, dynamic> assignment) {
    return GestureDetector(
      onTap: () {
        // Navigate to assignment details
        Navigator.of(context).pushNamed(
          Routes.assignment,
          arguments: assignment,
        );
      },
      child: Container(
        width: 100,
        height: 100,
        decoration: BoxDecoration(
          color: AppColors.cardBackgroundColor,
          borderRadius: AppBorderRadius.assignmentCard,
          boxShadow: AppShadows.small,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Assignment Icon
            Container(
              padding: const EdgeInsets.all(AppSpacing.xSmall),
              decoration: BoxDecoration(
                color: (assignment['color'] as Color).withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                assignment['icon'] as IconData,
                size: AppComponents.iconMedium,
                color: assignment['color'] as Color,
              ),
            ),

            const SizedBox(height: AppSpacing.xSmall),

            // Assignment Title
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: AppSpacing.xxSmall),
              child: Text(
                assignment['title'] as String,
                style: AppTypography.labelSmall.copyWith(
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),

            // Submissions Count Badge
            if (assignment['submissionsCount'] > 0)
              Container(
                margin: const EdgeInsets.only(top: AppSpacing.xxSmall),
                padding: const EdgeInsets.symmetric(
                  horizontal: AppSpacing.xSmall,
                  vertical: 2,
                ),
                decoration: BoxDecoration(
                  color: AppColors.warningColor,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  '${assignment['submissionsCount']}',
                  style: AppTypography.caption.copyWith(
                    color: AppColors.onPrimaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
