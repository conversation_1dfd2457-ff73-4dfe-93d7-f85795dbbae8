import 'package:flutter/material.dart';
import 'package:eschool_teacher/ui/design_system/design_system.dart';
import 'package:eschool_teacher/data/repositories/teacher_profile_repository.dart';
import 'package:eschool_teacher/data/models/teacher_profile.dart';

/// Teacher header widget displaying profile information
/// Matches the design from the provided screenshot
class TeacherHeaderWidget extends StatelessWidget {
  const TeacherHeaderWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<TeacherProfile>(
      future: TeacherProfileRepository().getProfile(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return _buildLoadingHeader();
        }

        if (snapshot.hasError || !snapshot.hasData) {
          return _buildErrorHeader();
        }

        final teacher = snapshot.data!;
        return _buildTeacherHeader(context, teacher);
      },
    );
  }

  Widget _buildTeacherHeader(BuildContext context, TeacherProfile teacher) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppSpacing.medium),
      decoration: BoxDecoration(
        color: AppColors.cardBackgroundColor,
        borderRadius: AppBorderRadius.teacherCard,
        boxShadow: AppShadows.teacherCard,
      ),
      child: Row(
        children: [
          // Profile Image
          AppAvatar(
            imageUrl: teacher.profileImageUrl,
            name: teacher.fullName,
            size: AppComponents.profileImageSize,
          ),

          const SizedBox(width: AppSpacing.medium),

          // Teacher Info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Teacher Name with prefix
                Text(
                  'أ/ ${teacher.fullName}',
                  style: AppTypography.teacherName,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),

                const SizedBox(height: AppSpacing.xxSmall),

                // Primary Subject Badge
                if (teacher.subjectsTaught.isNotEmpty)
                  AppStatusBadge(
                    text: 'مدرس ${teacher.subjectsTaught.first.subject.title}',
                    color: AppColors.primaryColor,
                    textColor: AppColors.onPrimaryColor,
                  ),

                const SizedBox(height: AppSpacing.xxSmall),

                // Experience and Rating
                Row(
                  children: [
                    Icon(
                      Icons.star,
                      size: AppComponents.iconXSmall,
                      color: AppColors.warningColor,
                    ),
                    const SizedBox(width: AppSpacing.xxSmall),
                    Text(
                      teacher.formattedRating,
                      style: AppTypography.labelSmall.copyWith(
                        color: AppColors.warningColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(width: AppSpacing.small),
                    Text(
                      '•',
                      style: AppTypography.labelSmall.copyWith(
                        color: AppColors.onSurfaceColor,
                      ),
                    ),
                    const SizedBox(width: AppSpacing.small),
                    Text(
                      teacher.experienceText,
                      style: AppTypography.labelSmall.copyWith(
                        color: AppColors.onSurfaceColor,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Status Indicator
          Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(
              color: teacher.isActive ? AppColors.successColor : AppColors.disabledColor,
              shape: BoxShape.circle,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingHeader() {
    return Container(
      width: double.infinity,
      height: AppComponents.teacherHeaderHeight,
      padding: const EdgeInsets.all(AppSpacing.medium),
      decoration: BoxDecoration(
        color: AppColors.cardBackgroundColor,
        borderRadius: AppBorderRadius.teacherCard,
        boxShadow: AppShadows.teacherCard,
      ),
      child: Row(
        children: [
          // Profile Image Placeholder
          Container(
            width: AppComponents.profileImageSize,
            height: AppComponents.profileImageSize,
            decoration: BoxDecoration(
              color: AppColors.shimmerBaseColor,
              shape: BoxShape.circle,
            ),
          ),

          const SizedBox(width: AppSpacing.medium),

          // Text Placeholders
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: 150,
                  height: 20,
                  decoration: BoxDecoration(
                    color: AppColors.shimmerBaseColor,
                    borderRadius: AppBorderRadius.roundedSmall,
                  ),
                ),
                const SizedBox(height: AppSpacing.xSmall),
                Container(
                  width: 100,
                  height: 16,
                  decoration: BoxDecoration(
                    color: AppColors.shimmerBaseColor,
                    borderRadius: AppBorderRadius.roundedSmall,
                  ),
                ),
                const SizedBox(height: AppSpacing.xSmall),
                Container(
                  width: 120,
                  height: 14,
                  decoration: BoxDecoration(
                    color: AppColors.shimmerBaseColor,
                    borderRadius: AppBorderRadius.roundedSmall,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppSpacing.medium),
      decoration: BoxDecoration(
        color: AppColors.cardBackgroundColor,
        borderRadius: AppBorderRadius.teacherCard,
        boxShadow: AppShadows.teacherCard,
      ),
      child: Row(
        children: [
          AppAvatar(
            name: 'مدرس',
            size: AppComponents.profileImageSize,
          ),
          const SizedBox(width: AppSpacing.medium),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'أ/ مدرس',
                  style: AppTypography.teacherName,
                ),
                const SizedBox(height: AppSpacing.xxSmall),
                AppStatusBadge(
                  text: 'مدرس',
                  color: AppColors.primaryColor,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
