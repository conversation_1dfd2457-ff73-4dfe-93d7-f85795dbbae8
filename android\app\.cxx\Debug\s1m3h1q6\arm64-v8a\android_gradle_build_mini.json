{"buildFiles": ["C:\\Users\\<USER>\\Documents\\vs code\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Documents\\vs code\\eschool-a765196ccefa1cc6909debea30ad995293fa6802\\android\\app\\.cxx\\Debug\\s1m3h1q6\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Documents\\vs code\\eschool-a765196ccefa1cc6909debea30ad995293fa6802\\android\\app\\.cxx\\Debug\\s1m3h1q6\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}