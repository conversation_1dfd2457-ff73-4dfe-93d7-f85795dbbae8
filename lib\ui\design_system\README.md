# ESchool Student App Design System

A comprehensive design system extracted from the existing Flutter student app that provides consistent colors, typography, spacing, components, and themes for reuse across multiple applications.

## 📋 Table of Contents

- [Overview](#overview)
- [Installation](#installation)
- [Quick Start](#quick-start)
- [Design Tokens](#design-tokens)
- [Components](#components)
- [Theme Configuration](#theme-configuration)
- [Usage Examples](#usage-examples)
- [File Structure](#file-structure)
- [Contributing](#contributing)

## 🎨 Overview

This design system extracts all existing design patterns from the ESchool student app to ensure consistency and reusability without changing the current look and feel. It includes:

- **Color System**: Primary, secondary, surface, and status colors
- **Typography**: Poppins font family with consistent sizes and weights
- **Spacing System**: Uniform spacing values and padding patterns
- **Component Library**: 25+ reusable UI components
- **Theme Configuration**: Complete Material Design 3 theme setup
- **Responsive Design**: Adaptive layouts and responsive spacing

## 📦 Installation

1. Copy the `design_system` folder to your Flutter project:
   ```
   lib/ui/design_system/
   ```

2. Install required dependencies in your `pubspec.yaml`:
   ```yaml
   dependencies:
     flutter:
       sdk: flutter
     google_fonts: ^6.1.0
   ```

3. Import the design system:
   ```dart
   import 'package:your_app/ui/design_system/design_system.dart';
   ```

## 🚀 Quick Start

### Using Design Tokens

```dart
import 'package:your_app/ui/design_system/design_system.dart';

Container(
  color: AppColors.primaryColor,
  padding: EdgeInsets.all(AppSpacing.medium),
  child: Text(
    'Hello World',
    style: AppTypography.bodyMedium,
  ),
)
```

### Using Components

```dart
// Button
AppPrimaryButton(
  text: 'Submit',
  onPressed: () {},
)

// Text Field
AppTextField(
  labelText: 'Email',
  hintText: 'Enter your email',
  controller: emailController,
)

// Bottom Sheet
AppModalBottomSheet.show(
  context: context,
  title: 'Settings',
  child: SettingsForm(),
)
```

### Setting Up Theme

```dart
MaterialApp(
  theme: AppTheme.lightTheme,
  darkTheme: AppTheme.darkTheme,
  themeMode: ThemeMode.system,
  home: MyHomePage(),
)
```

## 🎯 Design Tokens

### Colors

| Token | Value | Usage |
|-------|-------|-------|
| `AppColors.primaryColor` | `#22577A` | Primary brand color |
| `AppColors.secondaryColor` | `#212121` | Secondary text and elements |
| `AppColors.surfaceColor` | `#F6F6F6` | Surface backgrounds |
| `AppColors.pageBackgroundColor` | `#FFFFFF` | Page backgrounds |
| `AppColors.errorColor` | `#FF6769` | Error states |
| `AppColors.greenColor` | `#4CAF50` | Success states |
| `AppColors.orangeColor` | `#FF9800` | Warning states |
| `AppColors.blueColor` | `#2196F3` | Info states |

### Typography

| Style | Font Size | Font Weight | Usage |
|-------|-----------|-------------|-------|
| `AppTypography.heading1` | 24px | Bold | Page titles |
| `AppTypography.heading2` | 20px | SemiBold | Section titles |
| `AppTypography.heading3` | 18px | SemiBold | Subsection titles |
| `AppTypography.bodyLarge` | 16px | Regular | Body text (large) |
| `AppTypography.bodyMedium` | 14px | Regular | Body text (default) |
| `AppTypography.bodySmall` | 12px | Regular | Small body text |
| `AppTypography.labelLarge` | 14px | Medium | Labels |
| `AppTypography.buttonMedium` | 14px | Medium | Button text |

### Spacing

| Token | Value | Usage |
|-------|-------|-------|
| `AppSpacing.xxSmall` | 4px | Minimal spacing |
| `AppSpacing.xSmall` | 8px | Small spacing |
| `AppSpacing.small` | 12px | Default small spacing |
| `AppSpacing.medium` | 16px | Default spacing |
| `AppSpacing.large` | 20px | Large spacing |
| `AppSpacing.xLarge` | 24px | Extra large spacing |
| `AppSpacing.xxLarge` | 32px | Maximum spacing |

### Border Radius

| Token | Value | Usage |
|-------|-------|-------|
| `AppBorderRadius.small` | 4px | Small elements |
| `AppBorderRadius.medium` | 8px | Buttons |
| `AppBorderRadius.large` | 10px | Text fields, cards |
| `AppBorderRadius.xLarge` | 15px | Large components |
| `AppBorderRadius.xxLarge` | 20px | Containers |

## 🧩 Components

### Buttons

| Component | Description |
|-----------|-------------|
| `AppPrimaryButton` | Main action button with primary color |
| `AppSecondaryButton` | Secondary action button with outline style |
| `AppCustomRoundedButton` | Highly customizable button |
| `AppGoogleAuthButton` | Google authentication button |
| `AppCloseButton` | Close/dismiss button |
| `AppTextButton` | Text-only button |
| `AppIconButton` | Icon button |

**Example:**
```dart
AppPrimaryButton(
  text: 'Save Changes',
  icon: Icon(Icons.save),
  onPressed: () => saveData(),
  isLoading: isSubmitting,
)
```

### Text Fields

| Component | Description |
|-----------|-------------|
| `AppTextField` | Standard text input field |
| `AppSearchTextField` | Search input with search icon |
| `AppBottomSheetTextField` | Text field optimized for bottom sheets |
| `AppVerificationCodeField` | OTP/PIN code input |
| `AppDropdownTextField` | Dropdown selection field |

**Example:**
```dart
AppTextField(
  labelText: 'Full Name',
  hintText: 'Enter your full name',
  controller: nameController,
  validator: (value) => value?.isEmpty == true ? 'Required' : null,
)
```

### Bottom Sheets

| Component | Description |
|-----------|-------------|
| `AppModalBottomSheet` | Base modal bottom sheet |
| `AppFormBottomSheet` | Form container with submit/cancel actions |
| `AppVerificationCodeBottomSheet` | OTP verification interface |
| `AppListSelectionBottomSheet` | List selection interface |

**Example:**
```dart
AppFormBottomSheet.show(
  context: context,
  title: 'Add New Item',
  children: [
    AppTextField(labelText: 'Name'),
    AppTextField(labelText: 'Description'),
  ],
  onSubmit: () => handleSubmit(),
)
```

### Other Components

| Component | Description |
|-----------|-------------|
| `AppCard` | Container with elevation and rounded corners |
| `AppSurface` | Flat surface container |
| `AppNoDataContainer` | Empty state with optional action |
| `AppLoadingContainer` | Shimmer loading placeholder |
| `AppErrorContainer` | Error state with retry option |
| `AppStatusBadge` | Status indicator badge |
| `AppListTile` | Consistent list item |
| `AppAvatar` | User avatar component |

## 🎨 Theme Configuration

The design system provides complete Material Design 3 theme configuration:

```dart
MaterialApp(
  theme: AppTheme.lightTheme,
  darkTheme: AppTheme.darkTheme,
  themeMode: ThemeMode.system,
  // Your app configuration
)
```

### Theme Features

- **Consistent Colors**: All colors mapped to Material Design color roles
- **Typography Scale**: Complete text theme with Poppins font family
- **Component Themes**: Pre-configured styles for all Material components
- **Dark Mode Support**: Automatic dark theme with proper color adaptations
- **Accessibility**: WCAG compliant color contrasts and touch targets

## 📖 Usage Examples

### Creating a Form

```dart
class UserProfileForm extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: AppSpacing.paddingM,
      child: Column(
        children: [
          Text(
            'Profile Information',
            style: AppTypography.heading2,
          ),
          SizedBox(height: AppSpacing.medium),
          AppTextField(
            labelText: 'Full Name',
            hintText: 'Enter your full name',
          ),
          SizedBox(height: AppSpacing.medium),
          AppTextField(
            labelText: 'Email',
            hintText: 'Enter your email',
            keyboardType: TextInputType.emailAddress,
          ),
          SizedBox(height: AppSpacing.large),
          AppPrimaryButton(
            text: 'Save Profile',
            onPressed: () => saveProfile(),
          ),
        ],
      ),
    );
  }
}
```

### Creating a Card Layout

```dart
AppCard(
  child: Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Text(
        'Course Title',
        style: AppTypography.heading3,
      ),
      SizedBox(height: AppSpacing.xSmall),
      Text(
        'Course description goes here...',
        style: AppTypography.bodyMedium,
      ),
      SizedBox(height: AppSpacing.medium),
      Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          AppStatusBadge(
            text: 'Active',
            status: AppStatusBadgeType.success,
          ),
          AppTextButton(
            text: 'View Details',
            onPressed: () => viewDetails(),
          ),
        ],
      ),
    ],
  ),
)
```

### Showing a Bottom Sheet

```dart
void showAddItemSheet() {
  AppFormBottomSheet.show(
    context: context,
    title: 'Add New Assignment',
    children: [
      AppTextField(
        labelText: 'Assignment Title',
        hintText: 'Enter assignment title',
      ),
      SizedBox(height: AppSpacing.medium),
      AppTextField(
        labelText: 'Description',
        hintText: 'Enter description',
        maxLines: 3,
      ),
      SizedBox(height: AppSpacing.medium),
      AppDropdownTextField<String>(
        labelText: 'Subject',
        hintText: 'Select subject',
        items: subjects.map((subject) => 
          DropdownMenuItem(value: subject, child: Text(subject))
        ).toList(),
        onChanged: (value) => selectedSubject = value,
      ),
    ],
    onSubmit: () => saveAssignment(),
  );
}
```

## 📁 File Structure

```
lib/ui/design_system/
├── design_system.dart              # Main export file
├── app_design_system.dart          # Core design tokens
├── app_theme.dart                  # Flutter theme configuration
├── components/
│   ├── app_buttons.dart            # Button components
│   ├── app_text_fields.dart        # Text field components
│   ├── app_bottom_sheets.dart      # Bottom sheet components
│   └── app_components.dart         # Other UI components
└── README.md                       # This documentation
```

## 🎨 Customization

### Extending Colors

```dart
// Add custom colors to your app
class MyAppColors extends AppColors {
  static const Color customBlue = Color(0xFF1976D2);
  static const Color customGreen = Color(0xFF388E3C);
}
```

### Creating Custom Components

```dart
class MyCustomButton extends StatelessWidget {
  // Use design system tokens
  @override
  Widget build(BuildContext context) {
    return AppCustomRoundedButton(
      backgroundColor: AppColors.primaryColor,
      borderRadius: AppBorderRadius.roundedLarge,
      padding: AppSpacing.buttonPadding,
      child: Text(
        'Custom Button',
        style: AppTypography.buttonMedium,
      ),
      onTap: onPressed,
    );
  }
}
```

## 🚀 Benefits

- **Consistency**: Unified design language across all screens
- **Efficiency**: Faster development with pre-built components
- **Maintainability**: Central location for all design decisions
- **Scalability**: Easy to extend and modify
- **Reusability**: Can be used in multiple applications
- **Accessibility**: Built-in accessibility considerations
- **Theme Support**: Automatic light/dark mode support

## 📝 Contributing

When adding new components or modifying existing ones:

1. Follow the existing naming conventions
2. Use design tokens instead of hardcoded values
3. Ensure accessibility compliance
4. Add comprehensive documentation
5. Test in both light and dark themes
6. Maintain consistency with existing components

## 📄 License

This design system is extracted from the ESchool student app and follows the same licensing terms as the original application.

---

**Created from ESchool Student App** | **Extracted Design Patterns** | **Reusable Design System**
