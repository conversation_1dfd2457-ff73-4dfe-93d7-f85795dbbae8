class EducationStage {
  final int id;
  final String name;
  final String description;
  final int sortOrder;

  EducationStage({
    required this.id,
    required this.name,
    this.description = '',
    this.sortOrder = 0,
  });

  // Copy constructor for immutability
  EducationStage copyWith({
    int? id,
    String? name,
    String? description,
    int? sortOrder,
  }) {
    return EducationStage(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      sortOrder: sortOrder ?? this.sortOrder,
    );
  }

  factory EducationStage.fromJson(Map<String, dynamic> json) {
    return EducationStage(
      id: json['id'] is String ? int.parse(json['id']) : json['id'] as int,
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      sortOrder: json['sort_order'] ?? json['sortOrder'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'sort_order': sortOrder,
      'sortOrder': sortOrder, // For backward compatibility
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is EducationStage && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'EducationStage(id: $id, name: $name, description: $description, sortOrder: $sortOrder)';
  }
}
