import 'package:flutter/material.dart';
import '../app_design_system.dart';

/// Reusable UI components for the teacher app
/// 
/// This library provides consistent component styling with Arabic RTL support
/// and teacher-specific UI patterns.

/// Card component with consistent styling
class AppCard extends StatelessWidget {
  const AppCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.backgroundColor,
    this.borderRadius,
    this.elevation,
    this.onTap,
  });

  final Widget child;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final Color? backgroundColor;
  final BorderRadius? borderRadius;
  final double? elevation;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin ?? const EdgeInsets.all(AppSpacing.xSmall),
      child: Material(
        color: backgroundColor ?? AppColors.cardBackgroundColor,
        borderRadius: borderRadius ?? AppBorderRadius.teacherCard,
        elevation: elevation ?? 2,
        shadowColor: AppColors.primaryColor.withOpacity(0.1),
        child: InkWell(
          onTap: onTap,
          borderRadius: borderRadius ?? AppBorderRadius.teacherCard,
          child: Padding(
            padding: padding ?? AppSpacing.teacherCardPadding,
            child: child,
          ),
        ),
      ),
    );
  }
}

/// Surface component for flat containers
class AppSurface extends StatelessWidget {
  const AppSurface({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.backgroundColor,
    this.borderRadius,
    this.border,
  });

  final Widget child;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final Color? backgroundColor;
  final BorderRadius? borderRadius;
  final Border? border;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      padding: padding ?? AppSpacing.paddingM,
      decoration: BoxDecoration(
        color: backgroundColor ?? AppColors.surfaceColor,
        borderRadius: borderRadius ?? AppBorderRadius.roundedMedium,
        border: border,
      ),
      child: child,
    );
  }
}

/// No data container for empty states
class AppNoDataContainer extends StatelessWidget {
  const AppNoDataContainer({
    super.key,
    required this.message,
    this.icon,
    this.actionButton,
  });

  final String message;
  final IconData? icon;
  final Widget? actionButton;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.xxLarge),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (icon != null) ...[
              Icon(
                icon,
                size: AppComponents.iconXLarge,
                color: AppColors.onSurfaceColor,
              ),
              const SizedBox(height: AppSpacing.medium),
            ],
            Text(
              message,
              style: AppTypography.bodyLarge.copyWith(
                color: AppColors.onSurfaceColor,
              ),
              textAlign: TextAlign.center,
            ),
            if (actionButton != null) ...[
              const SizedBox(height: AppSpacing.large),
              actionButton!,
            ],
          ],
        ),
      ),
    );
  }
}

/// Error container for error states
class AppErrorContainer extends StatelessWidget {
  const AppErrorContainer({
    super.key,
    required this.message,
    this.onRetry,
    this.retryButtonText = 'إعادة المحاولة',
  });

  final String message;
  final VoidCallback? onRetry;
  final String retryButtonText;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.xxLarge),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.error_outline,
              size: AppComponents.iconXLarge,
              color: AppColors.errorColor,
            ),
            const SizedBox(height: AppSpacing.medium),
            Text(
              message,
              style: AppTypography.bodyLarge.copyWith(
                color: AppColors.errorColor,
              ),
              textAlign: TextAlign.center,
            ),
            if (onRetry != null) ...[
              const SizedBox(height: AppSpacing.large),
              ElevatedButton(
                onPressed: onRetry,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.errorColor,
                  foregroundColor: AppColors.onPrimaryColor,
                ),
                child: Text(retryButtonText),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// Loading container with shimmer effect
class AppLoadingContainer extends StatelessWidget {
  const AppLoadingContainer({
    super.key,
    this.height = 100,
    this.width,
    this.borderRadius,
  });

  final double height;
  final double? width;
  final BorderRadius? borderRadius;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      width: width,
      decoration: BoxDecoration(
        color: AppColors.shimmerBaseColor,
        borderRadius: borderRadius ?? AppBorderRadius.roundedMedium,
      ),
      child: const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.primaryColor),
        ),
      ),
    );
  }
}

/// Status badge component
class AppStatusBadge extends StatelessWidget {
  const AppStatusBadge({
    super.key,
    required this.text,
    required this.color,
    this.textColor,
    this.padding,
  });

  final String text;
  final Color color;
  final Color? textColor;
  final EdgeInsets? padding;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding ?? const EdgeInsets.symmetric(
        horizontal: AppSpacing.small,
        vertical: AppSpacing.xxSmall,
      ),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(AppComponents.subjectBadgeHeight / 2),
      ),
      child: Text(
        text,
        style: AppTypography.subjectBadge.copyWith(
          color: textColor ?? AppColors.onPrimaryColor,
        ),
      ),
    );
  }
}

/// List tile component with consistent styling
class AppListTile extends StatelessWidget {
  const AppListTile({
    super.key,
    this.leading,
    this.title,
    this.subtitle,
    this.trailing,
    this.onTap,
    this.padding,
  });

  final Widget? leading;
  final Widget? title;
  final Widget? subtitle;
  final Widget? trailing;
  final VoidCallback? onTap;
  final EdgeInsets? padding;

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: AppBorderRadius.roundedMedium,
        child: Padding(
          padding: padding ?? const EdgeInsets.symmetric(
            horizontal: AppSpacing.medium,
            vertical: AppSpacing.small,
          ),
          child: Row(
            children: [
              if (leading != null) ...[
                leading!,
                const SizedBox(width: AppSpacing.medium),
              ],
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (title != null) title!,
                    if (subtitle != null) ...[
                      const SizedBox(height: AppSpacing.xxSmall),
                      subtitle!,
                    ],
                  ],
                ),
              ),
              if (trailing != null) ...[
                const SizedBox(width: AppSpacing.medium),
                trailing!,
              ],
            ],
          ),
        ),
      ),
    );
  }
}

/// Divider component with consistent styling
class AppDivider extends StatelessWidget {
  const AppDivider({
    super.key,
    this.height,
    this.thickness,
    this.color,
    this.indent,
    this.endIndent,
  });

  final double? height;
  final double? thickness;
  final Color? color;
  final double? indent;
  final double? endIndent;

  @override
  Widget build(BuildContext context) {
    return Divider(
      height: height ?? 1,
      thickness: thickness ?? 1,
      color: color ?? AppColors.borderColor,
      indent: indent,
      endIndent: endIndent,
    );
  }
}

/// Avatar component for user profiles
class AppAvatar extends StatelessWidget {
  const AppAvatar({
    super.key,
    this.imageUrl,
    this.name,
    this.size = AppComponents.profileImageSize,
    this.backgroundColor,
    this.textColor,
  });

  final String? imageUrl;
  final String? name;
  final double size;
  final Color? backgroundColor;
  final Color? textColor;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: backgroundColor ?? AppColors.primaryColor,
        shape: BoxShape.circle,
        border: Border.all(
          color: AppColors.borderColor,
          width: 1,
        ),
      ),
      child: ClipOval(
        child: imageUrl != null && imageUrl!.isNotEmpty
            ? Image.network(
                imageUrl!,
                width: size,
                height: size,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return _buildInitials();
                },
              )
            : _buildInitials(),
      ),
    );
  }

  Widget _buildInitials() {
    final initials = name?.isNotEmpty == true
        ? name!.split(' ').take(2).map((e) => e[0]).join().toUpperCase()
        : '؟';
    
    return Container(
      width: size,
      height: size,
      color: backgroundColor ?? AppColors.primaryColor,
      child: Center(
        child: Text(
          initials,
          style: AppTypography.labelLarge.copyWith(
            color: textColor ?? AppColors.onPrimaryColor,
            fontSize: size * 0.4,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }
}
