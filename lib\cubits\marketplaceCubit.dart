import 'package:bloc/bloc.dart';
import 'package:eschool/cubits/marketplaceState.dart';
import 'package:eschool/data/models/learning_resource.dart';
import 'package:eschool/data/models/subject.dart';
import 'package:eschool/data/models/subject_level.dart';
import 'package:eschool/data/repositories/digitalAssetRepository.dart';
import 'package:eschool/domain/repositories/subject_repository_interface.dart';
import 'package:flutter/material.dart';

class MarketplaceCubit extends Cubit<MarketplaceState> {
  final SubjectRepositoryInterface _subjectRepository;
  final DigitalAssetRepository _digitalAssetRepository;

  MarketplaceCubit({
    required SubjectRepositoryInterface subjectRepository,
    required DigitalAssetRepository digitalAssetRepository
  })
      : _subjectRepository = subjectRepository,
        _digitalAssetRepository = digitalAssetRepository,
        super(MarketplaceInitial());

  void fetchResources() async {
    emit(MarketplaceLoading());
    try {
      // Get subjects from repository
      final List<Subject> subjects = await _subjectRepository.getSubjects();

      // Get digital assets from repository
      final List<LearningResource> resources = await _digitalAssetRepository.fetchDigitalAssets();

      // If no resources found, fallback to dummy data for testing
      final List<LearningResource> finalResources = resources.isEmpty
          ? _getDummyResources(subjects)
          : resources;

      // Extract subjects and levels for filtering
      final List<String> subjectNames = finalResources
          .where((r) => r.subject != null)
          .map((r) => r.subject!.title)
          .toSet()
          .toList();
      final List<String> levelNames = finalResources.map((r) => r.level).toSet().toList();

      emit(MarketplaceSuccess(
        resources: finalResources,
        filteredResources: finalResources,
        subjects: subjectNames,
        levels: levelNames,
        cartItems: const [],
      ));
    } catch (e) {
      debugPrint('MarketplaceCubit: Error fetching resources: $e');
      emit(MarketplaceFailure(error: e.toString()));
    }
  }

  void searchResources(String query) {
    if (state is MarketplaceSuccess) {
      final currentState = state as MarketplaceSuccess;
      final resources = currentState.resources;

      if (query.isEmpty) {
        emit(currentState.copyWith(
          filteredResources: resources,
          searchQuery: '',
        ));
        return;
      }

      final filteredResources = resources.where((resource) {
        return resource.title.toLowerCase().contains(query.toLowerCase()) ||
            resource.description.toLowerCase().contains(query.toLowerCase()) ||
            resource.tags.any((tag) => tag.toLowerCase().contains(query.toLowerCase()));
      }).toList();

      emit(currentState.copyWith(
        filteredResources: filteredResources,
        searchQuery: query,
      ));
    }
  }

  void filterResources({List<String>? selectedSubjects, List<String>? selectedLevels}) {
    if (state is MarketplaceSuccess) {
      final currentState = state as MarketplaceSuccess;
      final resources = currentState.resources;

      List<LearningResource> filteredResources = resources;

      // Apply subject filter
      if (selectedSubjects != null && selectedSubjects.isNotEmpty) {
        filteredResources = filteredResources.where((resource) {
          return resource.subject != null && selectedSubjects.contains(resource.subject!.title);
        }).toList();
      }

      // Apply level filter
      if (selectedLevels != null && selectedLevels.isNotEmpty) {
        filteredResources = filteredResources.where((resource) {
          return selectedLevels.contains(resource.level);
        }).toList();
      }

      // Also apply search query if exists
      if (currentState.searchQuery.isNotEmpty) {
        final query = currentState.searchQuery;
        filteredResources = filteredResources.where((resource) {
          return resource.title.toLowerCase().contains(query.toLowerCase()) ||
              resource.description.toLowerCase().contains(query.toLowerCase()) ||
              resource.tags.any((tag) => tag.toLowerCase().contains(query.toLowerCase()));
        }).toList();
      }

      emit(currentState.copyWith(
        filteredResources: filteredResources,
        selectedSubjects: selectedSubjects ?? currentState.selectedSubjects,
        selectedLevels: selectedLevels ?? currentState.selectedLevels,
      ));
    }
  }

  void clearFilters() {
    if (state is MarketplaceSuccess) {
      final currentState = state as MarketplaceSuccess;
      final resources = currentState.resources;

      // Apply only search query if exists
      List<LearningResource> filteredResources = resources;
      if (currentState.searchQuery.isNotEmpty) {
        final query = currentState.searchQuery;
        filteredResources = resources.where((resource) {
          return resource.title.toLowerCase().contains(query.toLowerCase()) ||
              resource.description.toLowerCase().contains(query.toLowerCase()) ||
              resource.tags.any((tag) => tag.toLowerCase().contains(query.toLowerCase()));
        }).toList();
      }

      emit(currentState.copyWith(
        filteredResources: filteredResources,
        selectedSubjects: [],
        selectedLevels: [],
      ));
    }
  }

  void addToCart(LearningResource resource) {
    if (state is MarketplaceSuccess) {
      final currentState = state as MarketplaceSuccess;
      final List<LearningResource> updatedCart = List.from(currentState.cartItems);

      // Check if already in cart
      if (!updatedCart.any((item) => item.id == resource.id)) {
        updatedCart.add(resource);
      }

      emit(currentState.copyWith(cartItems: updatedCart));
    }
  }

  void removeFromCart(int resourceId) {
    if (state is MarketplaceSuccess) {
      final currentState = state as MarketplaceSuccess;
      final List<LearningResource> updatedCart = List.from(currentState.cartItems)
        ..removeWhere((item) => item.id == resourceId);

      emit(currentState.copyWith(cartItems: updatedCart));
    }
  }

  void clearCart() {
    if (state is MarketplaceSuccess) {
      final currentState = state as MarketplaceSuccess;
      emit(currentState.copyWith(cartItems: []));
    }
  }

  void markAsPurchased(List<int> resourceIds) {
    if (state is MarketplaceSuccess) {
      final currentState = state as MarketplaceSuccess;

      // Update resources list
      final List<LearningResource> updatedResources = currentState.resources.map((resource) {
        if (resourceIds.contains(resource.id)) {
          return resource.copyWith(isPurchased: true);
        }
        return resource;
      }).toList();

      // Update filtered resources too
      final List<LearningResource> updatedFilteredResources = currentState.filteredResources.map((resource) {
        if (resourceIds.contains(resource.id)) {
          return resource.copyWith(isPurchased: true);
        }
        return resource;
      }).toList();

      // Remove purchased items from cart
      final List<LearningResource> updatedCart = currentState.cartItems
        .where((item) => !resourceIds.contains(item.id))
        .toList();

      emit(currentState.copyWith(
        resources: updatedResources,
        filteredResources: updatedFilteredResources,
        cartItems: updatedCart,
      ));
    }
  }

  // Helper methods for future use
  // List<String> _extractSubjects(List<LearningResource> resources) {
  //   final subjects = resources.map((resource) => resource.subject.title).toSet().toList();
  //   subjects.sort();
  //   return subjects;
  // }

  // List<String> _extractLevels(List<LearningResource> resources) {
  //   final levels = resources.map((resource) => resource.level).toSet().toList();
  //   levels.sort();
  //   return levels;
  // }

  // String _getCurrency(BuildContext? context) {
  //   if (context != null) {
  //     return UiUtils.getTranslatedLabel(context, currencyKey);
  //   }
  //   return "QAR"; // Default currency
  // }

  List<LearningResource> _getDummyResources(List<Subject> availableSubjects) {
    // Use subjects from the repository, default to original subjects if none available
    final List<Subject> subjects = availableSubjects.isNotEmpty
        ? availableSubjects
        : [
            Subject(
              id: 1,
              title: "Mathematics",
              imageUrl: "",
              subjectLevels: [
                SubjectLevel(id: 1, name: "Beginner", subjectId: 1),
                SubjectLevel(id: 2, name: "Intermediate", subjectId: 1),
                SubjectLevel(id: 3, name: "Advanced", subjectId: 1)
              ]
            ),
            Subject(
              id: 2,
              title: "Physics",
              imageUrl: "",
              subjectLevels: [
                SubjectLevel(id: 4, name: "Beginner", subjectId: 2),
                SubjectLevel(id: 5, name: "Intermediate", subjectId: 2),
                SubjectLevel(id: 6, name: "Advanced", subjectId: 2)
              ]
            ),
            Subject(
              id: 3,
              title: "Chemistry",
              imageUrl: "",
              subjectLevels: [
                SubjectLevel(id: 7, name: "Intermediate", subjectId: 3),
                SubjectLevel(id: 8, name: "Advanced", subjectId: 3)
              ]
            ),
            Subject(
              id: 4,
              title: "English",
              imageUrl: "",
              subjectLevels: [
                SubjectLevel(id: 9, name: "Beginner", subjectId: 4),
                SubjectLevel(id: 10, name: "Intermediate", subjectId: 4),
                SubjectLevel(id: 11, name: "Advanced", subjectId: 4)
              ]
            ),
            Subject(
              id: 5,
              title: "Biology",
              imageUrl: "",
              subjectLevels: [
                SubjectLevel(id: 12, name: "Intermediate", subjectId: 5),
                SubjectLevel(id: 13, name: "Advanced", subjectId: 5)
              ]
            ),
            Subject(
              id: 6,
              title: "Computer Science",
              imageUrl: "",
              subjectLevels: [
                SubjectLevel(id: 14, name: "Beginner", subjectId: 6)
              ]
            ),
            Subject(
              id: 7,
              title: "History",
              imageUrl: "",
              subjectLevels: [
                SubjectLevel(id: 15, name: "Beginner", subjectId: 7),
                SubjectLevel(id: 16, name: "Intermediate", subjectId: 7)
              ]
            ),
          ];

    return [
      LearningResource(
        id: 1,
        title: "Mathematics Fundamentals",
        description: "A comprehensive guide to basic mathematics concepts including algebra, geometry, and arithmetic.",
        price: 9.99,
        type: "digital_asset",
        currency: "QAR",
        imageUrl: "assets/images/mathematics.jpeg",
        fileUrl: "assets/files/mathematics_fundamentals.pdf",
        fileType: "PDF",
        fileSize: "5.2 MB",
        tags: ["Mathematics", "Algebra", "Geometry"],
        subject: subjects.firstWhere((s) => s.title.contains("Math") || s.id == 1,
            orElse: () => Subject(id: 1, title: "Mathematics", imageUrl: "")),
        level: "Beginner",
        uploadDate: DateTime.now().subtract(const Duration(days: 15)),
        uploadedBy: "Prof. Alan Smith",
      ),
      LearningResource(
        id: 2,
        title: "Advanced Calculus",
        description: "Explore advanced calculus topics including limits, derivatives, and integration techniques.",
        price: 14.99,
        type: "digital_asset",
        currency: "QAR",
        imageUrl: "assets/images/calculus.jpeg",
        fileUrl: "assets/files/advanced_calculus.pdf",
        fileType: "PDF",
        fileSize: "8.7 MB",
        tags: ["Mathematics", "Calculus", "Advanced"],
        subject: subjects.firstWhere((s) => s.title.contains("Math") || s.id == 1,
            orElse: () => Subject(id: 1, title: "Mathematics", imageUrl: "")),
        level: "Advanced",
        uploadDate: DateTime.now().subtract(const Duration(days: 10)),
        uploadedBy: "Dr. Sarah Johnson",
      ),
      LearningResource(
        id: 3,
        title: "Introduction to Physics",
        description: "Learn basic physics concepts including motion, forces, energy, and simple machines.",
        price: 8.99,
        type: "digital_asset",
        currency: "QAR",
        imageUrl: "assets/images/physics.jpeg",
        fileUrl: "assets/files/intro_physics.pptx",
        fileType: "PPTX",
        fileSize: "12.3 MB",
        tags: ["Physics", "Science", "Motion"],
        subject: subjects.firstWhere((s) => s.title.contains("Physics") || s.id == 2,
            orElse: () => Subject(id: 2, title: "Physics", imageUrl: "")),
        level: "Beginner",
        uploadDate: DateTime.now().subtract(const Duration(days: 20)),
        uploadedBy: "Prof. David Chen",
      ),
      LearningResource(
        id: 4,
        title: "Chemistry Experiments",
        description: "A collection of safe and exciting chemistry experiments that can be performed at home or in the classroom.",
        price: 11.99,
        type: "digital_asset",
        currency: "QAR",
        imageUrl: "assets/images/chemistry.jpeg",
        fileUrl: "assets/files/chemistry_experiments.pdf",
        fileType: "PDF",
        fileSize: "7.5 MB",
        tags: ["Chemistry", "Experiments", "Science"],
        subject: subjects.firstWhere((s) => s.title.contains("Chemistry") || s.id == 3,
            orElse: () => Subject(id: 3, title: "Chemistry", imageUrl: "")),
        level: "Intermediate",
        uploadDate: DateTime.now().subtract(const Duration(days: 8)),
        uploadedBy: "Dr. Emily Wong",
      ),
      LearningResource(
        id: 5,
        title: "English Grammar Guide",
        description: "Master English grammar rules, punctuation, and writing techniques for effective communication.",
        price: 7.99,
        type: "digital_asset",
        currency: "QAR",
        imageUrl: "assets/images/english.jpeg",
        fileUrl: "assets/files/english_grammar.epub",
        fileType: "EPUB",
        fileSize: "3.2 MB",
        tags: ["English", "Grammar", "Writing"],
        subject: subjects.firstWhere((s) => s.title.contains("English") || s.id == 4,
            orElse: () => Subject(id: 4, title: "English", imageUrl: "")),
        level: "Intermediate",
        uploadDate: DateTime.now().subtract(const Duration(days: 12)),
        uploadedBy: "Prof. Jane Miller",
      ),
      LearningResource(
        id: 6,
        title: "Biology: Human Anatomy",
        description: "Detailed study of human anatomy with illustrations and explanations of all major body systems.",
        price: 13.99,
        type: "digital_asset",
        currency: "QAR",
        imageUrl: "assets/images/biology.jpeg",
        fileUrl: "assets/files/human_anatomy.pdf",
        fileType: "PDF",
        fileSize: "15.8 MB",
        tags: ["Biology", "Anatomy", "Human Body"],
        subject: subjects.firstWhere((s) => s.title.contains("Biology") || s.id == 5,
            orElse: () => Subject(id: 5, title: "Biology", imageUrl: "")),
        level: "Intermediate",
        uploadDate: DateTime.now().subtract(const Duration(days: 25)),
        uploadedBy: "Dr. Robert Garcia",
      ),
      LearningResource(
        id: 7,
        title: "Computer Science Basics",
        description: "Introduction to computer science concepts, algorithms, and basic programming logic.",
        price: 12.99,
        type: "digital_asset",
        currency: "QAR",
        imageUrl: "assets/images/cs.jpeg",
        fileUrl: "assets/files/cs_basics.zip",
        fileType: "ZIP",
        fileSize: "24.3 MB",
        tags: ["Computer Science", "Programming", "Algorithms"],
        subject: subjects.firstWhere((s) => s.title.contains("Computer") || s.id == 6,
            orElse: () => Subject(id: 6, title: "Computer Science", imageUrl: "")),
        level: "Beginner",
        uploadDate: DateTime.now().subtract(const Duration(days: 5)),
        uploadedBy: "Prof. Michael Lee",
      ),
      LearningResource(
        id: 8,
        title: "World History: Ancient Civilizations",
        description: "Explore the rise and fall of ancient civilizations from Mesopotamia to Rome.",
        price: 9.99,
        type: "digital_asset",
        currency: "QAR",
        imageUrl: "assets/images/history.jpeg",
        fileUrl: "assets/files/ancient_civilizations.pdf",
        fileType: "PDF",
        fileSize: "10.1 MB",
        tags: ["History", "Ancient", "Civilizations"],
        subject: subjects.firstWhere((s) => s.title.contains("History") || s.id == 7,
            orElse: () => Subject(id: 7, title: "History", imageUrl: "")),
        level: "Beginner",
        uploadDate: DateTime.now().subtract(const Duration(days: 18)),
        uploadedBy: "Dr. Thomas Clark",
      ),
    ];
  }
}