name: eschool_teacher
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ">=3.6.0 <4.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  flutter_bloc: ^9.1.0
  equatable: ^2.0.5
  flutter_svg: ^2.0.17
  google_fonts: ^6.2.1
  dotted_border: ^2.1.0
  lottie: ^3.2.0
  dio: ^5.7.0
  showcaseview: ^4.0.1
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  table_calendar: ^3.2.0
  firebase_core: ^3.13.0
  firebase_messaging: ^15.2.5
  awesome_notifications: ^0.10.1
  cached_network_image: ^3.4.1
  shimmer: ^3.0.0
  permission_handler: ^12.0.0+1
  url_launcher: ^6.3.1
  flutter_html: ^3.0.0
  timeago: ^3.7.0
  open_filex: ^4.7.0
  package_info_plus: ^8.3.0
  file_picker: ^10.1.2
  external_path: ^2.2.0
  flutter_animate: ^4.5.2
  shared_preferences: ^2.5.3
  flutter_pdfview: ^1.4.0
  readmore: ^3.0.0
  image_picker: ^1.1.2
  any_link_preview: ^3.0.3
  flutter_keyboard_visibility: ^6.0.0
  intl: any
  bloc: any
  meta: any
  path_provider: any

dependency_overrides:
  intl: any

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  flutter_launcher_icons: ^0.14.3
  change_app_package_name: ^1.5.0

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/appLogo.png"
  remove_alpha_ios: true


# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - google_fonts/
    - assets/languages/
    - assets/animations/


  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
