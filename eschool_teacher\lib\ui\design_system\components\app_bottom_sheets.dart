import 'package:flutter/material.dart';
import '../app_design_system.dart';
import 'app_buttons.dart';

/// Reusable bottom sheet components for the teacher app
/// 
/// This library provides consistent bottom sheet styling with Arabic RTL support
/// and teacher-specific modal patterns.

/// Base modal bottom sheet component
class AppModalBottomSheet extends StatelessWidget {
  const AppModalBottomSheet({
    super.key,
    required this.child,
    this.title,
    this.showCloseButton = true,
    this.padding,
    this.maxHeight,
    this.minHeight,
  });

  final Widget child;
  final String? title;
  final bool showCloseButton;
  final EdgeInsets? padding;
  final double? maxHeight;
  final double? minHeight;

  static Future<T?> show<T>({
    required BuildContext context,
    required Widget child,
    String? title,
    bool showCloseButton = true,
    EdgeInsets? padding,
    double? maxHeight,
    double? minHeight,
    bool isDismissible = true,
    bool enableDrag = true,
  }) {
    return showModalBottomSheet<T>(
      context: context,
      isScrollControlled: true,
      isDismissible: isDismissible,
      enableDrag: enableDrag,
      backgroundColor: Colors.transparent,
      builder: (context) => AppModalBottomSheet(
        title: title,
        showCloseButton: showCloseButton,
        padding: padding,
        maxHeight: maxHeight,
        minHeight: minHeight,
        child: child,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final defaultMaxHeight = screenHeight * AppComponents.bottomSheetMaxHeight;
    final defaultMinHeight = screenHeight * AppComponents.bottomSheetMinHeight;

    return Container(
      constraints: BoxConstraints(
        maxHeight: maxHeight ?? defaultMaxHeight,
        minHeight: minHeight ?? defaultMinHeight,
      ),
      decoration: BoxDecoration(
        color: AppColors.pageBackgroundColor,
        borderRadius: AppBorderRadius.bottomSheet,
        boxShadow: AppShadows.large,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.only(top: AppSpacing.xSmall),
            decoration: BoxDecoration(
              color: AppColors.borderColor,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // Header
          if (title != null || showCloseButton)
            Container(
              padding: const EdgeInsets.all(AppSpacing.medium),
              child: Row(
                children: [
                  if (title != null)
                    Expanded(
                      child: Text(
                        title!,
                        style: AppTypography.heading3,
                        textAlign: TextAlign.center,
                      ),
                    ),
                  if (showCloseButton)
                    AppCloseButton(
                      onPressed: () => Navigator.of(context).pop(),
                      size: AppComponents.iconSmall,
                    ),
                ],
              ),
            ),
          
          // Content
          Flexible(
            child: Padding(
              padding: padding ?? AppSpacing.bottomSheetPadding,
              child: child,
            ),
          ),
        ],
      ),
    );
  }
}

/// Form bottom sheet for teacher data entry
class AppFormBottomSheet extends StatelessWidget {
  const AppFormBottomSheet({
    super.key,
    required this.title,
    required this.children,
    this.submitButtonText = 'حفظ',
    this.onSubmit,
    this.isLoading = false,
    this.showCancelButton = true,
  });

  final String title;
  final List<Widget> children;
  final String submitButtonText;
  final VoidCallback? onSubmit;
  final bool isLoading;
  final bool showCancelButton;

  static Future<T?> show<T>({
    required BuildContext context,
    required String title,
    required List<Widget> children,
    String submitButtonText = 'حفظ',
    VoidCallback? onSubmit,
    bool isLoading = false,
    bool showCancelButton = true,
  }) {
    return AppModalBottomSheet.show<T>(
      context: context,
      title: title,
      showCloseButton: false,
      child: AppFormBottomSheet(
        title: title,
        children: children,
        submitButtonText: submitButtonText,
        onSubmit: onSubmit,
        isLoading: isLoading,
        showCancelButton: showCancelButton,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Form fields
        ...children,
        
        const SizedBox(height: AppSpacing.large),
        
        // Action buttons
        Row(
          children: [
            if (showCancelButton) ...[
              Expanded(
                child: AppSecondaryButton(
                  onPressed: isLoading ? null : () => Navigator.of(context).pop(),
                  text: 'إلغاء',
                ),
              ),
              const SizedBox(width: AppSpacing.medium),
            ],
            Expanded(
              child: AppPrimaryButton(
                onPressed: onSubmit,
                text: submitButtonText,
                isLoading: isLoading,
              ),
            ),
          ],
        ),
      ],
    );
  }
}

/// List selection bottom sheet
class AppListSelectionBottomSheet<T> extends StatelessWidget {
  const AppListSelectionBottomSheet({
    super.key,
    required this.title,
    required this.items,
    required this.itemBuilder,
    this.selectedItem,
    this.onItemSelected,
    this.searchHint,
    this.emptyMessage = 'لا توجد عناصر',
  });

  final String title;
  final List<T> items;
  final Widget Function(BuildContext context, T item, bool isSelected) itemBuilder;
  final T? selectedItem;
  final ValueChanged<T>? onItemSelected;
  final String? searchHint;
  final String emptyMessage;

  static Future<T?> show<T>({
    required BuildContext context,
    required String title,
    required List<T> items,
    required Widget Function(BuildContext context, T item, bool isSelected) itemBuilder,
    T? selectedItem,
    ValueChanged<T>? onItemSelected,
    String? searchHint,
    String emptyMessage = 'لا توجد عناصر',
  }) {
    return AppModalBottomSheet.show<T>(
      context: context,
      title: title,
      child: AppListSelectionBottomSheet<T>(
        title: title,
        items: items,
        itemBuilder: itemBuilder,
        selectedItem: selectedItem,
        onItemSelected: onItemSelected,
        searchHint: searchHint,
        emptyMessage: emptyMessage,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (items.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(AppSpacing.xxLarge),
          child: Text(
            emptyMessage,
            style: AppTypography.bodyMedium.copyWith(
              color: AppColors.onSurfaceColor,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      );
    }

    return ListView.separated(
      shrinkWrap: true,
      itemCount: items.length,
      separatorBuilder: (context, index) => const Divider(
        height: 1,
        color: AppColors.borderColor,
      ),
      itemBuilder: (context, index) {
        final item = items[index];
        final isSelected = item == selectedItem;
        
        return InkWell(
          onTap: () {
            onItemSelected?.call(item);
            Navigator.of(context).pop(item);
          },
          child: Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppSpacing.medium,
              vertical: AppSpacing.small,
            ),
            child: itemBuilder(context, item, isSelected),
          ),
        );
      },
    );
  }
}
