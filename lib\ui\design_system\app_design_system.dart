import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

/// Comprehensive Design System for the ESchool Student App
/// 
/// This design system extracts all existing design patterns from the app
/// to ensure consistency and reusability across multiple applications.
/// 
/// Usage:
/// ```dart
/// AppDesignSystem.colors.primaryColor
/// AppDesignSystem.typography.bodyMedium
/// AppDesignSystem.spacing.small
/// ```
class AppDesignSystem { 
  AppDesignSystem._();

  static final AppColors colors = AppColors._();
  static final AppTypography typography = AppTypography._();
  static final AppSpacing spacing = AppSpacing._();
  static final AppBorderRadius borderRadius = AppBorderRadius._();
  static final AppShadows shadows = AppShadows._();
  static final AppComponents components = AppComponents._();
}

/// Color palette extracted from the existing app design
class AppColors {
  AppColors._();

  // Primary Brand Colors
  static const Color primaryColor = Color(0xFF22577A);
  static const Color secondaryColor = Color(0xFF212121);
  
  // Background Colors
  static const Color pageBackgroundColor = Color(0xFFFFFFFF);
  static const Color surfaceColor = Color(0xFFF6F6F6);
  
  // Status Colors
  static const Color errorColor = Color(0xFFFF6769);
  static const Color greenColor = Color(0xFF4CAF50);
  static const Color redColor = Color(0xFFF44336);
  static const Color orangeColor = Color(0xFFFF9800);
  static const Color blueColor = Color(0xFF2196F3);
  
  // UI Element Colors
  static const Color onSurfaceColor = Color(0xFF757575);
  static const Color onPrimaryColor = Color(0xFFFFFFFF);
  static const Color onSecondaryColor = Color(0xFFFFFFFF);
  static const Color hintTextColor = Color(0xFF9E9E9E);
    // Shimmer Loading Colors
  static const Color shimmerBaseColor = Color(0xFFE0E0E0);
  static const Color shimmerHighlightColor = Color(0xFFF5F5F5);
  static Color shimmerContentColor = Colors.white.withValues(alpha: 0.85);
    // Additional UI Colors
  static const Color borderColor = Color(0xFFE0E0E0);
  static const Color dividerColor = Color(0xFFBDBDBD);
  static const Color disabledColor = Color(0xFF9E9E9E);
}

/// Typography system based on Poppins font family usage in the app
class AppTypography {
  AppTypography._();

  // Base font family
  static const String fontFamily = 'Poppins';
  
  // Font weights
  static const FontWeight light = FontWeight.w300;
  static const FontWeight regular = FontWeight.w400;
  static const FontWeight medium = FontWeight.w500;
  static const FontWeight semiBold = FontWeight.w600;
  static const FontWeight bold = FontWeight.w700;
  
  // Heading styles
  static TextStyle get heading1 => GoogleFonts.poppins(
    fontSize: 24,
    fontWeight: bold,
    color: AppColors.secondaryColor,
    height: 1.2,
  );
  
  static TextStyle get heading2 => GoogleFonts.poppins(
    fontSize: 20,
    fontWeight: semiBold,
    color: AppColors.secondaryColor,
    height: 1.3,
  );
  
  static TextStyle get heading3 => GoogleFonts.poppins(
    fontSize: 18,
    fontWeight: semiBold,
    color: AppColors.secondaryColor,
    height: 1.3,
  );
  
  static TextStyle get heading4 => GoogleFonts.poppins(
    fontSize: 16,
    fontWeight: medium,
    color: AppColors.secondaryColor,
    height: 1.4,
  );
  
  // Body text styles
  static TextStyle get bodyLarge => GoogleFonts.poppins(
    fontSize: 16,
    fontWeight: regular,
    color: AppColors.secondaryColor,
    height: 1.5,
  );
  
  static TextStyle get bodyMedium => GoogleFonts.poppins(
    fontSize: 14,
    fontWeight: regular,
    color: AppColors.secondaryColor,
    height: 1.5,
  );
  
  static TextStyle get bodySmall => GoogleFonts.poppins(
    fontSize: 12,
    fontWeight: regular,
    color: AppColors.onSurfaceColor,
    height: 1.4,
  );
  
  // Label styles
  static TextStyle get labelLarge => GoogleFonts.poppins(
    fontSize: 14,
    fontWeight: medium,
    color: AppColors.secondaryColor,
    height: 1.4,
  );
  
  static TextStyle get labelMedium => GoogleFonts.poppins(
    fontSize: 12,
    fontWeight: medium,
    color: AppColors.onSurfaceColor,
    height: 1.3,
  );
  
  static TextStyle get labelSmall => GoogleFonts.poppins(
    fontSize: 11,
    fontWeight: medium,
    color: AppColors.hintTextColor,
    height: 1.3,
  );
  
  // Button text styles
  static TextStyle get buttonLarge => GoogleFonts.poppins(
    fontSize: 16,
    fontWeight: medium,
    color: AppColors.onPrimaryColor,
    height: 1.2,
  );
  
  static TextStyle get buttonMedium => GoogleFonts.poppins(
    fontSize: 14,
    fontWeight: medium,
    color: AppColors.onPrimaryColor,
    height: 1.2,
  );
  
  static TextStyle get buttonSmall => GoogleFonts.poppins(
    fontSize: 12,
    fontWeight: medium,
    color: AppColors.onPrimaryColor,
    height: 1.2,
  );
  
  // Special text styles
  static TextStyle get hint => GoogleFonts.poppins(
    fontSize: 14,
    fontWeight: regular,
    color: AppColors.hintTextColor,
    height: 1.4,
  );
  
  static TextStyle get error => GoogleFonts.poppins(
    fontSize: 12,
    fontWeight: regular,
    color: AppColors.errorColor,
    height: 1.3,
  );
  
  static TextStyle get caption => GoogleFonts.poppins(
    fontSize: 10,
    fontWeight: regular,
    color: AppColors.onSurfaceColor,
    height: 1.2,
  );
}

/// Consistent spacing system extracted from the app
class AppSpacing {
  AppSpacing._();

  // Base spacing unit
  static const double baseUnit = 4.0;
  
  // Spacing values
  static const double xxSmall = baseUnit;        // 4px
  static const double xSmall = baseUnit * 2;    // 8px
  static const double small = baseUnit * 3;     // 12px
  static const double medium = baseUnit * 4;    // 16px
  static const double large = baseUnit * 5;     // 20px
  static const double xLarge = baseUnit * 6;    // 24px
  static const double xxLarge = baseUnit * 8;   // 32px
  
  // Common spacing values found in the app
  static const double spacing5 = 5.0;
  static const double spacing8 = 8.0;
  static const double spacing10 = 10.0;
  static const double spacing12 = 12.0;
  static const double spacing15 = 15.0;
  static const double spacing16 = 16.0;
  static const double spacing20 = 20.0;
  static const double spacing25 = 25.0;
  static const double spacing30 = 30.0;
  
  // Edge insets
  static const EdgeInsets paddingXS = EdgeInsets.all(xxSmall);
  static const EdgeInsets paddingS = EdgeInsets.all(xSmall);
  static const EdgeInsets paddingM = EdgeInsets.all(medium);
  static const EdgeInsets paddingL = EdgeInsets.all(large);
  static const EdgeInsets paddingXL = EdgeInsets.all(xLarge);
  
  // Horizontal padding
  static const EdgeInsets paddingHorizontalS = EdgeInsets.symmetric(horizontal: xSmall);
  static const EdgeInsets paddingHorizontalM = EdgeInsets.symmetric(horizontal: medium);
  static const EdgeInsets paddingHorizontalL = EdgeInsets.symmetric(horizontal: large);
  
  // Vertical padding
  static const EdgeInsets paddingVerticalS = EdgeInsets.symmetric(vertical: xSmall);
  static const EdgeInsets paddingVerticalM = EdgeInsets.symmetric(vertical: medium);
  static const EdgeInsets paddingVerticalL = EdgeInsets.symmetric(vertical: large);
  
  // Common padding patterns from the app
  static const EdgeInsets textFieldPadding = EdgeInsets.symmetric(horizontal: 10, vertical: 12);
  static const EdgeInsets bottomSheetPadding = EdgeInsets.all(20);
  static const EdgeInsets buttonPadding = EdgeInsets.symmetric(horizontal: 24, vertical: 12);
  static const EdgeInsets cardPadding = EdgeInsets.all(16);
}

/// Border radius system from the app
class AppBorderRadius {
  AppBorderRadius._();

  static const double none = 0.0;
  static const double small = 4.0;
  static const double medium = 8.0;
  static const double large = 10.0;
  static const double xLarge = 15.0;
  static const double xxLarge = 20.0;
  static const double circular = 50.0;
  
  // Common border radius patterns
  static BorderRadius get roundedSmall => BorderRadius.circular(small);
  static BorderRadius get roundedMedium => BorderRadius.circular(medium);
  static BorderRadius get roundedLarge => BorderRadius.circular(large);
  static BorderRadius get roundedXLarge => BorderRadius.circular(xLarge);
  static BorderRadius get roundedXXLarge => BorderRadius.circular(xxLarge);
  static BorderRadius get roundedCircular => BorderRadius.circular(circular);
  
  // Specific component border radius
  static BorderRadius get textField => BorderRadius.circular(large);
  static BorderRadius get button => BorderRadius.circular(medium);
  static BorderRadius get card => BorderRadius.circular(large);
  static BorderRadius get bottomSheet => const BorderRadius.only(
    topLeft: Radius.circular(xLarge),
    topRight: Radius.circular(xLarge),
  );
}

/// Shadow system for elevation
class AppShadows {
  AppShadows._();

  static const List<BoxShadow> none = [];
  
  static const List<BoxShadow> small = [
    BoxShadow(
      color: Color(0x0F000000),
      offset: Offset(0, 1),
      blurRadius: 2,
      spreadRadius: 0,
    ),
  ];
  
  static const List<BoxShadow> medium = [
    BoxShadow(
      color: Color(0x1A000000),
      offset: Offset(0, 2),
      blurRadius: 4,
      spreadRadius: 0,
    ),
  ];
  
  static const List<BoxShadow> large = [
    BoxShadow(
      color: Color(0x26000000),
      offset: Offset(0, 4),
      blurRadius: 8,
      spreadRadius: 0,
    ),
  ];
  
  // Google Auth Button shadow (from existing component)
  static const List<BoxShadow> googleButton = [
    BoxShadow(
      color: Color(0x1A000000),
      offset: Offset(0, 1),
      blurRadius: 3,
      spreadRadius: 0,
    ),
  ];
}

/// Component library configuration
class AppComponents {
  AppComponents._();
  
  // Button configurations
  static const double buttonHeight = 48.0;
  static const double smallButtonHeight = 36.0;
  static const double largeButtonHeight = 56.0;
  
  // Text field configurations
  static const double textFieldHeight = 48.0;
  static const double textFieldBorderWidth = 1.0;
  
  // Bottom sheet configurations
  static const double bottomSheetMaxHeight = 0.9;
  static const double bottomSheetMinHeight = 0.3;
  
  // Icon sizes
  static const double iconXSmall = 16.0;
  static const double iconSmall = 20.0;
  static const double iconMedium = 24.0;
  static const double iconLarge = 32.0;
  static const double iconXLarge = 48.0;
}
