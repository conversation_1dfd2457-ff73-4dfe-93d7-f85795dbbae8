import 'package:eschool/ui/screens/assignment/assignmentScreen.dart';
import 'package:eschool/ui/screens/auth/authScreen.dart';
import 'package:eschool/ui/screens/auth/signUpScreen.dart';
import 'package:eschool/ui/screens/auth/studentLoginScreen.dart';
import 'package:eschool/ui/screens/chapterDetails/chapterDetailsScreen.dart';
import 'package:eschool/ui/screens/chat/chatMessagesScreen.dart';
import 'package:eschool/ui/screens/classLessons/classLessonsScreen.dart';
import 'package:eschool/ui/screens/contactUsScreen.dart';
import 'package:eschool/ui/screens/home/<USER>';
import 'package:eschool/ui/screens/packages/packagesScreen.dart';
import 'package:eschool/ui/screens/cart/cart_screen.dart' as shopping_cart;
import 'package:eschool/ui/screens/privacyPolicyScreen.dart';
import 'package:eschool/ui/screens/splashScreen.dart';
import 'package:eschool/ui/screens/studentProfileScreen.dart';
import 'package:eschool/ui/screens/editProfileScreen.dart';
import 'package:eschool/ui/screens/marketplace/marketplaceScreen.dart';
import 'package:eschool/ui/screens/marketplace/resourceDetailsScreen.dart';
import 'package:eschool/ui/screens/marketplace/cartScreen.dart' as marketplace_cart;
import 'package:flutter/cupertino.dart';

// ignore: avoid_classes_with_only_static_members
class Routes {
  static const String splash = "splash";
  static const String home = "/";

  static const String auth = "auth";
  static const String studentLogin = "studentLogin";
  static const String signUp = "signUp";

  static const String studentProfile = "studentprofile";
  static const String parentProfile = "parentprofile";

  static const String classLessons = "classLessons";
  static const String subjectDetails = "subjectdetails";
  static const String topics = "topics";
  static const String chapterDetails = "chapterdetails";

  static const String notifications = "notifications";
  static const String noticeBoard = "noticeboard";

  static const String assignment = "assignment";

  static const String settings = "settings";

  static const String contactUs = "contactus";
  static const String aboutUs = "aboutus";
  static const String privacyPolicy = "privacypolicy";
  static const String termsCondition = "termscondition";
  static const String payment = "payment";
  static const String paymentHistory = "paymenthistory";
  static const String paymentDetails = "paymentdetails";
  static const String paymentWebview = "paymentwebview";
  static const String paymentStripeWebview = "paymentstripewebview";
  static const String paymentStatus = "paymentstatus";
  static const String cart = "cart";
  static const String packages = "packages";
  static const String packageDetails = "packageDetails";

  // Chat routes
  static const String chatMessages = "chatMessages";

  // The following routes are declared to silence errors but not implemented
  // They redirect to home screen
  static const String studentFeePaymentDueScreen = "studentFeePaymentDueScreen";
  static const String feesTransaction = "feesTransaction";
  static const String feesDetails = "feesDetails";
  static const String termsAndCondition = "termsAndCondition";
  static const String chatUserProfile = "chatUserProfile";
  static const String chatUserSearch = "chatUserSearch";
  static const String imageFileView = "imageFileView";
  static const String pdfFileView = "pdfFileView";
  static const String playVideo = "playVideo";
  static const String resultOnline = "resultOnline";
  static const String result = "result";
  static const String examTimeTable = "examTimeTable";
  static const String examOnline = "examOnline";
  static const String paymentVerify = "paymentVerify";
  static const String eventDetails = "eventDetails";
  static const String webViewPaymentScreen = "webViewPaymentScreen";
  static const String manageLeaves = "manageLeaves";
  static const String editProfile = "editProfile";
  static const String academicCalendar = "academicCalendar";
  static const String topicDetails = "topicDetails";

  // Add marketplace related routes
  static const String marketplace = "marketplace";
  static const String resourceDetails = "resourceDetails";
  static const String marketplaceCart = "marketplaceCart";

  // Add login route if it doesn't exist
  static const String login = "login";

  static String currentRoute = splash;

  static Route<dynamic> onGenerateRoute(RouteSettings routeSettings) {
    currentRoute = routeSettings.name ?? "";

    switch (routeSettings.name) {
      case splash:
        return SplashScreen.route(routeSettings);

      case home:
        return HomeScreen.route(routeSettings);

      case auth:
        return CupertinoPageRoute(builder: (_) => const AuthScreen());

      case studentLogin:
        return StudentLoginScreen.route(routeSettings);

      case signUp:
        return SignUpScreen.route(routeSettings);

      case assignment:
        return AssignmentScreen.route(routeSettings);

      case studentProfile:
        return StudentProfileScreen.route(routeSettings);

      case classLessons:
        return ClassLessonsScreen.route(routeSettings);

      case chapterDetails:
        return ChapterDetailsScreen.route(routeSettings);

      case contactUs:
        return ContactUsScreen.route(routeSettings);

      case privacyPolicy:
        return PrivacyPolicyScreen.route(routeSettings);

      case cart:
        return CupertinoPageRoute(builder: (_) => const shopping_cart.CartScreen());

      case packages:
        return PackagesScreen.route(routeSettings);

      case chatMessages:
        return ChatMessagesScreen.route(routeSettings);

      case editProfile:
        return EditProfileScreen.route(routeSettings);

      case marketplace:
        return MarketplaceScreen.route(routeSettings);

      case resourceDetails:
        return ResourceDetailsScreen.route(routeSettings);

      case marketplaceCart:
        return marketplace_cart.CartScreen.route(routeSettings);

      default:
        // For any undefined routes, redirect to home
        debugPrint("Warning: Route '${routeSettings.name}' not defined - redirecting to home");
        return HomeScreen.route(routeSettings);
    }
  }
}
