import 'package:flutter/material.dart';
import '../app_design_system.dart';

/// Reusable button components extracted from the existing app design
/// 
/// This library provides consistent button styling that matches the current
/// app appearance while being reusable across multiple applications.

/// Primary button component based on existing ElevatedButton usage
class AppPrimaryButton extends StatelessWidget {
  const AppPrimaryButton({
    super.key,
    required this.onPressed,
    required this.text,
    this.isLoading = false,
    this.isEnabled = true,
    this.width,
    this.height,
    this.icon,
  });

  final VoidCallback? onPressed;
  final String text;
  final bool isLoading;
  final bool isEnabled;
  final double? width;
  final double? height;
  final Widget? icon;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width,
      height: height ?? AppComponents.buttonHeight,
      child: ElevatedButton.icon(
        onPressed: isEnabled && !isLoading ? onPressed : null,
        icon: isLoading 
            ? SizedBox(
                width: AppComponents.iconSmall,
                height: AppComponents.iconSmall,
                child: const CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.onPrimaryColor),
                ),
              )
            : icon ?? const SizedBox.shrink(),
        label: Text(
          text,
          style: AppTypography.buttonMedium,
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primaryColor,
          foregroundColor: AppColors.onPrimaryColor,
          disabledBackgroundColor: AppColors.disabledColor,
          disabledForegroundColor: AppColors.onSurfaceColor,
          elevation: 2,
          shadowColor: AppColors.primaryColor.withOpacity(0.3),
          shape: RoundedRectangleBorder(
            borderRadius: AppBorderRadius.button,
          ),
          padding: AppSpacing.buttonPadding,
        ),
      ),
    );
  }
}

/// Secondary button component with outline style
class AppSecondaryButton extends StatelessWidget {
  const AppSecondaryButton({
    super.key,
    required this.onPressed,
    required this.text,
    this.isLoading = false,
    this.isEnabled = true,
    this.width,
    this.height,
    this.icon,
  });

  final VoidCallback? onPressed;
  final String text;
  final bool isLoading;
  final bool isEnabled;
  final double? width;
  final double? height;
  final Widget? icon;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width,
      height: height ?? AppComponents.buttonHeight,
      child: OutlinedButton.icon(
        onPressed: isEnabled && !isLoading ? onPressed : null,
        icon: isLoading 
            ? SizedBox(
                width: AppComponents.iconSmall,
                height: AppComponents.iconSmall,
                child: const CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.primaryColor),
                ),
              )
            : icon ?? const SizedBox.shrink(),
        label: Text(
          text,
          style: AppTypography.buttonMedium.copyWith(
            color: AppColors.primaryColor,
          ),
        ),
        style: OutlinedButton.styleFrom(
          foregroundColor: AppColors.primaryColor,
          disabledForegroundColor: AppColors.disabledColor,
          side: const BorderSide(
            color: AppColors.primaryColor,
            width: 1.5,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: AppBorderRadius.button,
          ),
          padding: AppSpacing.buttonPadding,
        ),
      ),
    );
  }
}

/// Custom rounded button based on existing CustomRoundedButton component
class AppCustomRoundedButton extends StatelessWidget {
  const AppCustomRoundedButton({
    super.key,
    required this.onTap,
    required this.child,
    this.backgroundColor = AppColors.primaryColor,
    this.borderRadius,
    this.padding,
    this.width,
    this.height,
    this.elevation = 2,
    this.isEnabled = true,
  });

  final VoidCallback? onTap;
  final Widget child;
  final Color backgroundColor;
  final BorderRadius? borderRadius;
  final EdgeInsetsGeometry? padding;
  final double? width;
  final double? height;
  final double elevation;
  final bool isEnabled;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width,
      height: height ?? AppComponents.buttonHeight,
      child: Material(
        color: isEnabled ? backgroundColor : AppColors.disabledColor,
        elevation: elevation,
        borderRadius: borderRadius ?? AppBorderRadius.button,
        child: InkWell(
          onTap: isEnabled ? onTap : null,
          borderRadius: borderRadius ?? AppBorderRadius.button,
          child: Container(
            padding: padding ?? AppSpacing.buttonPadding,
            decoration: BoxDecoration(
              borderRadius: borderRadius ?? AppBorderRadius.button,
            ),
            child: Center(child: child),
          ),
        ),
      ),
    );
  }
}

/// Google Auth button based on existing GoogleAuthButton component
class AppGoogleAuthButton extends StatelessWidget {
  const AppGoogleAuthButton({
    super.key,
    required this.onPressed,
    this.text = 'Continue with Google',
    this.isLoading = false,
    this.width,
    this.height,
  });

  final VoidCallback? onPressed;
  final String text;
  final bool isLoading;
  final double? width;
  final double? height;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height ?? AppComponents.buttonHeight,
      decoration: BoxDecoration(
        color: AppColors.pageBackgroundColor,
        borderRadius: AppBorderRadius.button,
        boxShadow: AppShadows.googleButton,
        border: Border.all(
          color: AppColors.borderColor,
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: !isLoading ? onPressed : null,
          borderRadius: AppBorderRadius.button,
          child: Container(
            padding: AppSpacing.buttonPadding,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (isLoading) ...[
                  SizedBox(
                    width: AppComponents.iconSmall,
                    height: AppComponents.iconSmall,
                    child: const CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(AppColors.primaryColor),
                    ),
                  ),
                  const SizedBox(width: AppSpacing.small),
                ] else ...[
                  Image.asset(
                    'assets/images/google_logo.png', // Assuming Google logo exists
                    width: AppComponents.iconSmall,
                    height: AppComponents.iconSmall,
                    errorBuilder: (context, error, stackTrace) {
                      return Icon(
                        Icons.g_mobiledata,
                        size: AppComponents.iconSmall,
                        color: AppColors.primaryColor,
                      );
                    },
                  ),
                  const SizedBox(width: AppSpacing.small),
                ],
                Text(
                  text,
                  style: AppTypography.buttonMedium.copyWith(
                    color: AppColors.secondaryColor,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// Custom close button based on existing CustomCloseButton component
class AppCloseButton extends StatelessWidget {
  const AppCloseButton({
    super.key,
    required this.onPressed,
    this.size = AppComponents.iconMedium,
    this.backgroundColor = AppColors.surfaceColor,
    this.iconColor = AppColors.secondaryColor,
    this.hasBorder = true,
  });

  final VoidCallback onPressed;
  final double size;
  final Color backgroundColor;
  final Color iconColor;
  final bool hasBorder;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size + 16,
      height: size + 16,
      decoration: BoxDecoration(
        color: backgroundColor,
        shape: BoxShape.circle,
        border: hasBorder
            ? Border.all(
                color: AppColors.borderColor,
                width: 1,
              )
            : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(size),
          child: Icon(
            Icons.close,
            size: size,
            color: iconColor,
          ),
        ),
      ),
    );
  }
}

/// Text button component for less prominent actions
class AppTextButton extends StatelessWidget {
  const AppTextButton({
    super.key,
    required this.onPressed,
    required this.text,
    this.textColor = AppColors.primaryColor,
    this.isEnabled = true,
    this.fontSize,
  });

  final VoidCallback? onPressed;
  final String text;
  final Color textColor;
  final bool isEnabled;
  final double? fontSize;

  @override
  Widget build(BuildContext context) {
    return TextButton(
      onPressed: isEnabled ? onPressed : null,
      style: TextButton.styleFrom(
        foregroundColor: textColor,
        disabledForegroundColor: AppColors.disabledColor,
        padding: const EdgeInsets.symmetric(
          horizontal: AppSpacing.medium,
          vertical: AppSpacing.xSmall,
        ),
      ),
      child: Text(
        text,
        style: AppTypography.buttonMedium.copyWith(
          color: isEnabled ? textColor : AppColors.disabledColor,
          fontSize: fontSize,
        ),
      ),
    );
  }
}

/// Icon button component with consistent styling
class AppIconButton extends StatelessWidget {
  const AppIconButton({
    super.key,
    required this.onPressed,
    required this.icon,
    this.backgroundColor = Colors.transparent,
    this.iconColor = AppColors.secondaryColor,
    this.size = AppComponents.iconMedium,
    this.padding = AppSpacing.xSmall,
    this.borderRadius,
    this.isEnabled = true,
  });

  final VoidCallback? onPressed;
  final IconData icon;
  final Color backgroundColor;
  final Color iconColor;
  final double size;
  final double padding;
  final BorderRadius? borderRadius;
  final bool isEnabled;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: borderRadius ?? BorderRadius.circular(padding),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: isEnabled ? onPressed : null,
          borderRadius: borderRadius ?? BorderRadius.circular(padding),
          child: Padding(
            padding: EdgeInsets.all(padding),
            child: Icon(
              icon,
              size: size,
              color: isEnabled ? iconColor : AppColors.disabledColor,
            ),
          ),
        ),
      ),
    );
  }
}
