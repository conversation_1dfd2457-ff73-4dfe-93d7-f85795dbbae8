import 'dart:io';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:eschool/data/services/api_service.dart';

/// ApiClient acts as a facade for all API requests.
/// It centralizes endpoint definitions and provides a clean interface for repositories.
class ApiClient {
  final ApiService _apiService;
  
  // Base URL for the API
  static const String baseUrl = 'https://api.mahranstudio.com/api/student';
  
  // Singleton pattern
  static final ApiClient _instance = ApiClient._internal();
  
  // Factory constructor
  factory ApiClient() => _instance;
  
  // Internal constructor
  ApiClient._internal() : _apiService = ApiService();
  
  // ======== AUTH ENDPOINTS ========
  
  /// Login
  Future<Map<String, dynamic>> login(String email, String password) async {
    debugPrint('ApiClient: Attempting to login with email: $email');
    return await _apiService.postMultipart(
      '/login',
      fields: {
        'email': email,
        'password': password,
      },
    );
  }
  
  /// Register
  Future<Map<String, dynamic>> register({
    required String name, 
    required String email, 
    required String password, 
    required String passwordConfirmation,
    String? mobile,
  }) async {
    final fields = {
      'name': name,
      'email': email,
      'password': password,
      'password_confirmation': passwordConfirmation,
    };
    
    // Add mobile field if provided
    if (mobile != null && mobile.isNotEmpty) {
      fields['mobile'] = mobile;
    }
    
    return await _apiService.postMultipart(
      '/register',
      fields: fields,
    );
  }
  
  /// Verify email with code (after registration)
  Future<Map<String, dynamic>> verifyEmailRegistration(String code, {String? token}) async {
    return await _apiService.postMultipart(
      '/email/verify',
      token: token,
      fields: {
        'otp': code,
      },
    );
  }
  
  /// Resend email verification code
  Future<Map<String, dynamic>> resendEmailVerification(String email, {String? token}) async {
    return await _apiService.postMultipart(
      '/resend/email/verify',
      token: token,
      fields: {
        'email': email,
      },
    );
  }
  
  /// Send email verification OTP
  Future<Map<String, dynamic>> sendEmailVerificationOtp(String token) async {
    return await _apiService.postMultipart(
      '/email/verification-notification',
      token: token,
      fields: {},
    );
  }

  /// Send password reset OTP
  Future<Map<String, dynamic>> sendPasswordResetOtp(String email) async {
    return await _apiService.postMultipart(
      '/forgot/password',
      fields: {
        'email': email,
      },
    );
  }
  
  /// Verify password reset OTP
  Future<Map<String, dynamic>> verifyPasswordResetOtp(String email, String otp) async {
    return await _apiService.postMultipart(
      '/verify/reset/password/otp',
      fields: {
        'email': email,
        'otp': otp,
      },
    );
  }
  
  /// Reset password
  Future<Map<String, dynamic>> resetPassword({
    required String email, 
    required String password, 
    required String passwordConfirmation
  }) async {
    return await _apiService.postMultipart(
      '/reset/password',
      fields: {
        'email': email,
        'password': password,
        'password_confirmation': passwordConfirmation,
      },
    );
  }
  
  // ======== USER PROFILE ENDPOINTS ========
  
  /// Update user profile
  Future<Map<String, dynamic>> updateProfile({
    required String token, 
    required String name,
    String? mobile,
    File? image
  }) async {
    final fields = {
      '_method': 'patch',
      'name': name,
    };
    
    // Add mobile field if provided
    if (mobile != null && mobile.isNotEmpty) {
      fields['mobile'] = mobile;
    }
    
    List<MapEntry<String, File>>? files;
    if (image != null) {
      files = [MapEntry('image', image)];
    }
    
    return await _apiService.postMultipart(
      '/update/user/profile',
      token: token,
      fields: fields,
      files: files,
    );
  }
  
  /// Delete user account
  Future<Map<String, dynamic>> deleteAccount({
    required String token, 
    required String email,
    required String password
  }) async {
    debugPrint('ApiClient: Attempting to delete account for user: $email');
    return await _apiService.postMultipart(
      '/delete/user/profile',
      token: token,
      fields: {
        '_method': 'delete',
        'email': email,
        'password': password,
      },
    );
  }
  
  /// Soft delete user account (deactivate)
  Future<Map<String, dynamic>> softDeleteAccount({
    required String token, 
    required String email,
    required String password
  }) async {
    debugPrint('ApiClient: Attempting to soft delete (deactivate) account for user: $email');
    return await _apiService.postMultipart(
      '/soft-delete/user/profile',
      token: token,
      fields: {
        '_method': 'delete',
        'email': email,
        'password': password,
      },
    );
  }
  
  /// Get user profile information
  Future<Map<String, dynamic>> getProfile({required String token}) async {
    debugPrint('ApiClient: Fetching user profile with token: ${token.substring(0, 10)}...');
    try {
      final response = await _apiService.get(
        '/profile',
        token: token,
      );
      
      debugPrint('ApiClient: Profile response status: ${response['status'] ?? 'unknown'}');
      
      if (response.containsKey('user')) {
        debugPrint('ApiClient: Profile user data found. Keys: ${response['user']?.keys.toList() ?? 'null'}');
      } else {
        debugPrint('ApiClient: Profile response missing user data. Available keys: ${response.keys.toList()}');
      }
      
      return response;
    } catch (e) {
      debugPrint('ApiClient: Error fetching profile: ${e.toString()}');
      rethrow;
    }
  }
  
  /// Logout user (needed for compatibility)
  Future<Map<String, dynamic>> logout(String token) async {
    debugPrint('ApiClient: Attempting logout');
    return await _apiService.post(
      '/logout',
      token: token,
      bodyData: {},
    );
  }
  
  // ======== SOCIAL AUTH ENDPOINTS ========
  
  /// Google Login
  Future<Map<String, dynamic>> googleLogin(Map<String, dynamic> googleData) async {
    debugPrint('\n------ API CLIENT: GOOGLE LOGIN REQUEST ------');
    debugPrint('Starting Google login API request');
    
    try {
      // Convert dynamic values to strings for the fields parameter
      final Map<String, String> fields = {};
      googleData.forEach((key, value) {
        fields[key] = value?.toString() ?? '';
      });
      
      // Ensure required fields are present
      if (!fields.containsKey('id_token') || fields['id_token']?.isEmpty == true) {
        throw Exception('Missing or empty id_token required for Google authentication');
      }
      
      debugPrint('Request endpoint: $baseUrl/auth/google/callback');
      debugPrint('Fields being sent:');
      debugPrint('  google_id: ${fields['google_id']}');
      debugPrint('  email: ${fields['email']}');
      debugPrint('  name: ${fields['name']}');
      debugPrint('  id_token length: ${fields['id_token']?.length ?? 0}');
      debugPrint('  device_type: ${fields['device_type']}');
      
      // Use the correct endpoint from the API documentation
      final response = await _apiService.postMultipart(
        '/auth/google/callback',
        fields: fields,
      );
      
      debugPrint('Google login API response received');
      debugPrint('Response contains token: ${response.containsKey('token')}');
      debugPrint('Response contains user: ${response.containsKey('user')}');
      debugPrint('Response status: ${response['status'] ?? 'not provided'}');
      debugPrint('Response keys: ${response.keys.toList()}');
      
      if (response.containsKey('error')) {
        debugPrint('Response contains error: ${response['error']}');
        debugPrint('Error message: ${response['message'] ?? 'No message provided'}');
      }
      
      debugPrint('------ END API CLIENT: GOOGLE LOGIN REQUEST ------\n');
      return response;
    } catch (e) {
      debugPrint('ERROR in Google login API request: ${e.toString()}');
      debugPrint('Error type: ${e.runtimeType}');
      debugPrint('Stack trace: \n${StackTrace.current}');
      debugPrint('------ END API CLIENT: GOOGLE LOGIN REQUEST (ERROR) ------\n');
      rethrow;
    }
  }
  
  /// Google Sign Up
  Future<Map<String, dynamic>> googleSignUp(Map<String, dynamic> googleData) async {
    // For this API, login and signup use the same endpoint and parameters
    // The backend determines whether to create a new account or log in an existing one
    return googleLogin(googleData);
  }
  
  /// Get packages
  Future<Map<String, dynamic>> getPackages({
    String? token,
    int? subjectId,
    int? educationStageId
  }) async {
    Map<String, String> queryParams = {};
    
    if (subjectId != null) {
      queryParams['subject_id'] = subjectId.toString();
    }
    
    if (educationStageId != null) {
      queryParams['education_stage_id'] = educationStageId.toString();
    }
    
    return await _apiService.get(
      '/packages',
      token: token,
      queryParameters: queryParams.isNotEmpty ? queryParams : null,
    );
  }
}

