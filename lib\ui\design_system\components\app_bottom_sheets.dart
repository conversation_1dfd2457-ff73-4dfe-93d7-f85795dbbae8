import 'package:flutter/material.dart';
import '../app_design_system.dart';

/// Reusable modal bottom sheet components extracted from the existing app design
/// 
/// This library provides consistent bottom sheet styling that matches the current
/// app appearance while being reusable across multiple applications.

/// Base modal bottom sheet with consistent styling
class AppModalBottomSheet extends StatelessWidget {
  const AppModalBottomSheet({
    super.key,
    required this.child,
    this.title,
    this.showCloseButton = true,
    this.isDismissible = true,
    this.backgroundColor = AppColors.pageBackgroundColor,
    this.maxHeightFactor = AppComponents.bottomSheetMaxHeight,
    this.minHeightFactor = AppComponents.bottomSheetMinHeight,
    this.padding,
    this.onClose,
  });

  final Widget child;
  final String? title;
  final bool showCloseButton;
  final bool isDismissible;
  final Color backgroundColor;
  final double maxHeightFactor;
  final double minHeightFactor;
  final EdgeInsetsGeometry? padding;
  final VoidCallback? onClose;

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * maxHeightFactor,
        minHeight: MediaQuery.of(context).size.height * minHeightFactor,
      ),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: AppBorderRadius.bottomSheet,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.only(top: AppSpacing.xSmall),
            decoration: BoxDecoration(
              color: AppColors.onSurfaceColor.withOpacity(0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // Header
          if (title != null || showCloseButton) ...[
            Padding(
              padding: const EdgeInsets.all(AppSpacing.medium),
              child: Row(
                children: [
                  if (title != null) ...[
                    Expanded(
                      child: Text(
                        title!,
                        style: AppTypography.heading3,
                      ),
                    ),
                  ],
                  if (showCloseButton) ...[
                    IconButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                        onClose?.call();
                      },
                      icon: const Icon(
                        Icons.close,
                        color: AppColors.onSurfaceColor,
                      ),
                    ),
                  ],
                ],
              ),
            ),
            const Divider(
              color: AppColors.borderColor,
              height: 1,
            ),
          ],
          
          // Content
          Flexible(
            child: SingleChildScrollView(
              padding: padding ?? AppSpacing.bottomSheetPadding,
              child: child,
            ),
          ),
        ],
      ),
    );
  }

  /// Show the bottom sheet
  static Future<T?> show<T>({
    required BuildContext context,
    required Widget child,
    String? title,
    bool showCloseButton = true,
    bool isDismissible = true,
    Color backgroundColor = AppColors.pageBackgroundColor,
    double maxHeightFactor = AppComponents.bottomSheetMaxHeight,
    double minHeightFactor = AppComponents.bottomSheetMinHeight,
    EdgeInsetsGeometry? padding,
    VoidCallback? onClose,
    bool isScrollControlled = true,
    bool enableDrag = true,
  }) {
    return showModalBottomSheet<T>(
      context: context,
      isDismissible: isDismissible,
      enableDrag: enableDrag,
      isScrollControlled: isScrollControlled,
      backgroundColor: Colors.transparent,
      builder: (context) => AppModalBottomSheet(
        title: title,
        showCloseButton: showCloseButton,
        isDismissible: isDismissible,
        backgroundColor: backgroundColor,
        maxHeightFactor: maxHeightFactor,
        minHeightFactor: minHeightFactor,
        padding: padding,
        onClose: onClose,
        child: child,
      ),
    );
  }
}

/// Form bottom sheet for input forms
class AppFormBottomSheet extends StatelessWidget {
  const AppFormBottomSheet({
    super.key,
    required this.title,
    required this.children,
    this.submitButtonText = 'Submit',
    this.onSubmit,
    this.isSubmitEnabled = true,
    this.isLoading = false,
    this.showCloseButton = true,
    this.formKey,
  });

  final String title;
  final List<Widget> children;
  final String submitButtonText;
  final VoidCallback? onSubmit;
  final bool isSubmitEnabled;
  final bool isLoading;
  final bool showCloseButton;
  final GlobalKey<FormState>? formKey;

  @override
  Widget build(BuildContext context) {
    return AppModalBottomSheet(
      title: title,
      showCloseButton: showCloseButton,
      child: Form(
        key: formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            ...children,
            const SizedBox(height: AppSpacing.large),
            Padding(
              padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom,
              ),
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: AppColors.onSurfaceColor,
                        side: const BorderSide(color: AppColors.borderColor),
                        shape: RoundedRectangleBorder(
                          borderRadius: AppBorderRadius.button,
                        ),
                        padding: const EdgeInsets.symmetric(vertical: AppSpacing.medium),
                      ),
                      child: Text(
                        'Cancel',
                        style: AppTypography.buttonMedium.copyWith(
                          color: AppColors.onSurfaceColor,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: AppSpacing.medium),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: isSubmitEnabled && !isLoading ? onSubmit : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primaryColor,
                        foregroundColor: AppColors.onPrimaryColor,
                        disabledBackgroundColor: AppColors.disabledColor,
                        shape: RoundedRectangleBorder(
                          borderRadius: AppBorderRadius.button,
                        ),
                        padding: const EdgeInsets.symmetric(vertical: AppSpacing.medium),
                      ),
                      child: isLoading
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  AppColors.onPrimaryColor,
                                ),
                              ),
                            )
                          : Text(
                              submitButtonText,
                              style: AppTypography.buttonMedium,
                            ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Show the form bottom sheet
  static Future<T?> show<T>({
    required BuildContext context,
    required String title,
    required List<Widget> children,
    String submitButtonText = 'Submit',
    VoidCallback? onSubmit,
    bool isSubmitEnabled = true,
    bool isLoading = false,
    bool showCloseButton = true,
    GlobalKey<FormState>? formKey,
  }) {
    return AppModalBottomSheet.show<T>(
      context: context,
      title: title,
      showCloseButton: showCloseButton,
      child: AppFormBottomSheet(
        title: title,
        children: children,
        submitButtonText: submitButtonText,
        onSubmit: onSubmit,
        isSubmitEnabled: isSubmitEnabled,
        isLoading: isLoading,
        showCloseButton: false, // Handled by the form itself
        formKey: formKey,
      ),
    );
  }
}

/// Verification code bottom sheet based on existing pattern
class AppVerificationCodeBottomSheet extends StatefulWidget {
  const AppVerificationCodeBottomSheet({
    super.key,
    required this.title,
    required this.subtitle,
    required this.onCodeSubmitted,
    this.codeLength = 6,
    this.onResendCode,
    this.resendCodeText = 'Resend Code',
    this.autoSubmit = true,
  });

  final String title;
  final String subtitle;
  final Function(String) onCodeSubmitted;
  final int codeLength;
  final VoidCallback? onResendCode;
  final String resendCodeText;
  final bool autoSubmit;

  @override
  State<AppVerificationCodeBottomSheet> createState() => _AppVerificationCodeBottomSheetState();

  /// Show the verification code bottom sheet
  static Future<String?> show({
    required BuildContext context,
    required String title,
    required String subtitle,
    required Function(String) onCodeSubmitted,
    int codeLength = 6,
    VoidCallback? onResendCode,
    String resendCodeText = 'Resend Code',
    bool autoSubmit = true,
  }) {
    return AppModalBottomSheet.show<String>(
      context: context,
      isDismissible: false,
      child: AppVerificationCodeBottomSheet(
        title: title,
        subtitle: subtitle,
        onCodeSubmitted: onCodeSubmitted,
        codeLength: codeLength,
        onResendCode: onResendCode,
        resendCodeText: resendCodeText,
        autoSubmit: autoSubmit,
      ),
    );
  }
}

class _AppVerificationCodeBottomSheetState extends State<AppVerificationCodeBottomSheet> {
  final TextEditingController _codeController = TextEditingController();
  bool _isLoading = false;

  @override
  void dispose() {
    _codeController.dispose();
    super.dispose();
  }

  void _handleCodeSubmission(String code) async {
    if (code.length == widget.codeLength && !_isLoading) {
      setState(() => _isLoading = true);
      
      try {
        await widget.onCodeSubmitted(code);
        if (mounted) {
          Navigator.of(context).pop(code);
        }
      } catch (e) {
        // Handle error
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Header
        Text(
          widget.title,
          style: AppTypography.heading2,
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: AppSpacing.medium),
        
        Text(
          widget.subtitle,
          style: AppTypography.bodyMedium.copyWith(
            color: AppColors.onSurfaceColor,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: AppSpacing.xLarge),
        
        // Code input
        Container(
          padding: const EdgeInsets.symmetric(horizontal: AppSpacing.medium),
          child: TextField(
            controller: _codeController,
            keyboardType: TextInputType.number,
            textAlign: TextAlign.center,
            maxLength: widget.codeLength,
            enabled: !_isLoading,
            autofocus: true,
            onChanged: (value) {
              if (widget.autoSubmit && value.length == widget.codeLength) {
                _handleCodeSubmission(value);
              }
            },
            style: AppTypography.heading2.copyWith(
              letterSpacing: AppSpacing.medium,
            ),
            decoration: InputDecoration(
              counterText: '',
              hintText: '●' * widget.codeLength,
              hintStyle: AppTypography.heading2.copyWith(
                color: AppColors.hintTextColor,
                letterSpacing: AppSpacing.medium,
              ),
              filled: true,
              fillColor: AppColors.surfaceColor,
              border: OutlineInputBorder(
                borderRadius: AppBorderRadius.textField,
                borderSide: const BorderSide(
                  color: AppColors.borderColor,
                  width: 1,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: AppBorderRadius.textField,
                borderSide: const BorderSide(
                  color: AppColors.borderColor,
                  width: 1,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: AppBorderRadius.textField,
                borderSide: const BorderSide(
                  color: AppColors.primaryColor,
                  width: 2,
                ),
              ),
              contentPadding: const EdgeInsets.symmetric(
                vertical: AppSpacing.large,
              ),
            ),
          ),
        ),
        
        const SizedBox(height: AppSpacing.xLarge),
        
        // Submit button (if not auto-submit)
        if (!widget.autoSubmit) ...[
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _isLoading 
                  ? null 
                  : () => _handleCodeSubmission(_codeController.text),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryColor,
                foregroundColor: AppColors.onPrimaryColor,
                shape: RoundedRectangleBorder(
                  borderRadius: AppBorderRadius.button,
                ),
                padding: const EdgeInsets.symmetric(vertical: AppSpacing.medium),
              ),
              child: _isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          AppColors.onPrimaryColor,
                        ),
                      ),
                    )
                  : Text(
                      'Verify',
                      style: AppTypography.buttonMedium,
                    ),
            ),
          ),
          const SizedBox(height: AppSpacing.medium),
        ],
        
        // Resend code button
        if (widget.onResendCode != null) ...[
          TextButton(
            onPressed: _isLoading ? null : widget.onResendCode,
            child: Text(
              widget.resendCodeText,
              style: AppTypography.buttonMedium.copyWith(
                color: AppColors.primaryColor,
              ),
            ),
          ),
        ],
        
        const SizedBox(height: AppSpacing.medium),
      ],
    );
  }
}

/// List selection bottom sheet
class AppListSelectionBottomSheet<T> extends StatelessWidget {
  const AppListSelectionBottomSheet({
    super.key,
    required this.title,
    required this.items,
    required this.itemBuilder,
    this.selectedItem,
    this.onItemSelected,
    this.searchHint,
    this.emptyMessage = 'No items found',
  });

  final String title;
  final List<T> items;
  final Widget Function(T item, bool isSelected) itemBuilder;
  final T? selectedItem;
  final Function(T)? onItemSelected;
  final String? searchHint;
  final String emptyMessage;

  @override
  Widget build(BuildContext context) {
    return AppModalBottomSheet(
      title: title,
      child: Column(
        children: [
          // Search field (if provided)
          if (searchHint != null) ...[
            Container(
              margin: const EdgeInsets.only(bottom: AppSpacing.medium),
              child: TextField(
                decoration: InputDecoration(
                  hintText: searchHint,
                  hintStyle: AppTypography.hint,
                  prefixIcon: const Icon(
                    Icons.search,
                    color: AppColors.onSurfaceColor,
                  ),
                  filled: true,
                  fillColor: AppColors.surfaceColor,
                  border: OutlineInputBorder(
                    borderRadius: AppBorderRadius.textField,
                    borderSide: const BorderSide(
                      color: AppColors.borderColor,
                      width: 1,
                    ),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: AppBorderRadius.textField,
                    borderSide: const BorderSide(
                      color: AppColors.borderColor,
                      width: 1,
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: AppBorderRadius.textField,
                    borderSide: const BorderSide(
                      color: AppColors.primaryColor,
                      width: 2,
                    ),
                  ),
                  contentPadding: AppSpacing.textFieldPadding,
                ),
              ),
            ),
          ],
          
          // Items list
          if (items.isEmpty) ...[
            Padding(
              padding: const EdgeInsets.all(AppSpacing.xLarge),
              child: Text(
                emptyMessage,
                style: AppTypography.bodyMedium.copyWith(
                  color: AppColors.onSurfaceColor,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ] else ...[
            ...items.map((item) {
              final isSelected = item == selectedItem;
              return InkWell(
                onTap: () {
                  onItemSelected?.call(item);
                  Navigator.of(context).pop(item);
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppSpacing.medium,
                    vertical: AppSpacing.small,
                  ),
                  decoration: BoxDecoration(
                    color: isSelected ? AppColors.primaryColor.withOpacity(0.1) : null,
                    borderRadius: AppBorderRadius.roundedMedium,
                  ),
                  child: itemBuilder(item, isSelected),
                ),
              );
            }).toList(),
          ],
        ],
      ),
    );
  }

  /// Show the list selection bottom sheet
  static Future<T?> show<T>({
    required BuildContext context,
    required String title,
    required List<T> items,
    required Widget Function(T item, bool isSelected) itemBuilder,
    T? selectedItem,
    Function(T)? onItemSelected,
    String? searchHint,
    String emptyMessage = 'No items found',
  }) {
    return AppModalBottomSheet.show<T>(
      context: context,
      child: AppListSelectionBottomSheet<T>(
        title: title,
        items: items,
        itemBuilder: itemBuilder,
        selectedItem: selectedItem,
        onItemSelected: onItemSelected,
        searchHint: searchHint,
        emptyMessage: emptyMessage,
      ),
    );
  }
}
