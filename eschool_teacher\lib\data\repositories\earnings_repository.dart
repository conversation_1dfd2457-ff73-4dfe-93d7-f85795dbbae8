import 'package:eschool_teacher/data/models/earnings.dart';

class EarningsRepository {
  // Generate dummy earnings transactions
  static List<EarningsTransaction> _generateDummyTransactions() {
    final now = DateTime.now();
    final transactions = <EarningsTransaction>[];

    // Recent lesson earnings
    transactions.add(EarningsTransaction(
      id: 'txn_1',
      teacherId: 'teacher_1',
      type: TransactionType.lesson,
      status: TransactionStatus.completed,
      amount: 150.0,
      description: 'درس اللغة العربية - الوحدة الأولى',
      lessonId: '4',
      createdAt: now.subtract(const Duration(days: 1)),
      processedAt: now.subtract(const Duration(days: 1)),
    ));

    transactions.add(EarningsTransaction(
      id: 'txn_2',
      teacherId: 'teacher_1',
      type: TransactionType.lesson,
      status: TransactionStatus.completed,
      amount: 240.0,
      description: 'درس الرياضيات - مجموعة',
      lessonId: '2',
      createdAt: now.subtract(const Duration(days: 2)),
      processedAt: now.subtract(const Duration(days: 2)),
    ));

    transactions.add(EarningsTransaction(
      id: 'txn_3',
      teacherId: 'teacher_1',
      type: TransactionType.lesson,
      status: TransactionStatus.completed,
      amount: 180.0,
      description: 'درس الفيزياء - الحركة',
      lessonId: '3',
      createdAt: now.subtract(const Duration(days: 3)),
      processedAt: now.subtract(const Duration(days: 3)),
    ));

    // Bonus payment
    transactions.add(EarningsTransaction(
      id: 'txn_4',
      teacherId: 'teacher_1',
      type: TransactionType.bonus,
      status: TransactionStatus.completed,
      amount: 100.0,
      description: 'مكافأة أداء ممتاز',
      createdAt: now.subtract(const Duration(days: 5)),
      processedAt: now.subtract(const Duration(days: 5)),
    ));

    // Pending lesson earnings
    transactions.add(EarningsTransaction(
      id: 'txn_5',
      teacherId: 'teacher_1',
      type: TransactionType.lesson,
      status: TransactionStatus.pending,
      amount: 150.0,
      description: 'درس اللغة العربية - قيد المعالجة',
      lessonId: '1',
      createdAt: now.subtract(const Duration(hours: 2)),
    ));

    // Withdrawal
    transactions.add(EarningsTransaction(
      id: 'txn_6',
      teacherId: 'teacher_1',
      type: TransactionType.withdrawal,
      status: TransactionStatus.completed,
      amount: 500.0,
      description: 'سحب إلى الحساب البنكي',
      createdAt: now.subtract(const Duration(days: 7)),
      processedAt: now.subtract(const Duration(days: 6)),
    ));

    // More historical transactions
    for (int i = 7; i <= 20; i++) {
      transactions.add(EarningsTransaction(
        id: 'txn_$i',
        teacherId: 'teacher_1',
        type: TransactionType.lesson,
        status: TransactionStatus.completed,
        amount: 120.0 + (i % 3) * 30.0, // Varying amounts
        description: 'درس ${i % 3 == 0 ? 'اللغة العربية' : i % 3 == 1 ? 'الرياضيات' : 'الفيزياء'}',
        lessonId: 'lesson_$i',
        createdAt: now.subtract(Duration(days: 7 + i)),
        processedAt: now.subtract(Duration(days: 7 + i)),
      ));
    }

    return transactions;
  }

  static List<EarningsTransaction> _dummyTransactions = _generateDummyTransactions();

  // Generate dummy earnings summary
  static EarningsSummary _generateEarningsSummary() {
    final completedTransactions = _dummyTransactions.where((t) => 
        t.status == TransactionStatus.completed && t.isIncome).toList();
    final pendingTransactions = _dummyTransactions.where((t) => 
        t.status == TransactionStatus.pending && t.isIncome).toList();
    final withdrawals = _dummyTransactions.where((t) => 
        t.type == TransactionType.withdrawal && t.status == TransactionStatus.completed).toList();

    final totalEarnings = completedTransactions.fold(0.0, (sum, t) => sum + t.amount);
    final pendingEarnings = pendingTransactions.fold(0.0, (sum, t) => sum + t.amount);
    final totalWithdrawn = withdrawals.fold(0.0, (sum, t) => sum + t.amount);
    final availableBalance = totalEarnings - totalWithdrawn;

    // Calculate this month's stats
    final now = DateTime.now();
    final thisMonthTransactions = completedTransactions.where((t) => 
        t.createdAt.year == now.year && t.createdAt.month == now.month).toList();
    final earningsThisMonth = thisMonthTransactions.fold(0.0, (sum, t) => sum + t.amount);
    final lessonsThisMonth = thisMonthTransactions.where((t) => 
        t.type == TransactionType.lesson).length;

    final totalLessonsGiven = completedTransactions.where((t) => 
        t.type == TransactionType.lesson).length;

    return EarningsSummary(
      totalEarnings: totalEarnings,
      availableBalance: availableBalance,
      pendingEarnings: pendingEarnings,
      totalWithdrawn: totalWithdrawn,
      totalLessonsGiven: totalLessonsGiven,
      lessonsThisMonth: lessonsThisMonth,
      earningsThisMonth: earningsThisMonth,
      lastUpdated: DateTime.now(),
    );
  }

  // Get earnings summary
  Future<EarningsSummary> getEarningsSummary() async {
    await Future.delayed(const Duration(milliseconds: 500)); // Simulate network delay
    return _generateEarningsSummary();
  }

  // Get all transactions
  Future<List<EarningsTransaction>> getAllTransactions() async {
    await Future.delayed(const Duration(milliseconds: 400));
    return List.from(_dummyTransactions)..sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }

  // Get transactions by type
  Future<List<EarningsTransaction>> getTransactionsByType(TransactionType type) async {
    await Future.delayed(const Duration(milliseconds: 300));
    return _dummyTransactions.where((t) => t.type == type).toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }

  // Get transactions by status
  Future<List<EarningsTransaction>> getTransactionsByStatus(TransactionStatus status) async {
    await Future.delayed(const Duration(milliseconds: 300));
    return _dummyTransactions.where((t) => t.status == status).toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }

  // Get transactions by date range
  Future<List<EarningsTransaction>> getTransactionsByDateRange(DateTime start, DateTime end) async {
    await Future.delayed(const Duration(milliseconds: 400));
    return _dummyTransactions.where((t) {
      return t.createdAt.isAfter(start.subtract(const Duration(days: 1))) &&
             t.createdAt.isBefore(end.add(const Duration(days: 1)));
    }).toList()..sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }

  // Get recent transactions (last 10)
  Future<List<EarningsTransaction>> getRecentTransactions({int limit = 10}) async {
    await Future.delayed(const Duration(milliseconds: 300));
    final sorted = List.from(_dummyTransactions)
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
    return sorted.take(limit).toList();
  }

  // Get pending earnings
  Future<List<EarningsTransaction>> getPendingEarnings() async {
    await Future.delayed(const Duration(milliseconds: 300));
    return _dummyTransactions.where((t) => 
        t.status == TransactionStatus.pending && t.isIncome).toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }

  // Get monthly earnings for chart
  Future<Map<String, double>> getMonthlyEarnings(int year) async {
    await Future.delayed(const Duration(milliseconds: 500));
    final monthlyEarnings = <String, double>{};
    
    for (int month = 1; month <= 12; month++) {
      final monthTransactions = _dummyTransactions.where((t) => 
          t.createdAt.year == year && 
          t.createdAt.month == month && 
          t.status == TransactionStatus.completed &&
          t.isIncome).toList();
      
      final monthTotal = monthTransactions.fold(0.0, (sum, t) => sum + t.amount);
      
      final monthNames = [
        'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
        'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
      ];
      
      monthlyEarnings[monthNames[month - 1]] = monthTotal;
    }
    
    return monthlyEarnings;
  }

  // Add lesson earning (called when lesson is completed)
  Future<EarningsTransaction> addLessonEarning({
    required String lessonId,
    required double amount,
    required String description,
  }) async {
    await Future.delayed(const Duration(milliseconds: 600));
    
    final transaction = EarningsTransaction(
      id: 'txn_${DateTime.now().millisecondsSinceEpoch}',
      teacherId: 'teacher_1',
      type: TransactionType.lesson,
      status: TransactionStatus.pending, // Usually starts as pending
      amount: amount,
      description: description,
      lessonId: lessonId,
      createdAt: DateTime.now(),
    );
    
    _dummyTransactions.add(transaction);
    return transaction;
  }

  // Process pending transaction (simulate admin approval)
  Future<EarningsTransaction> processTransaction(String transactionId) async {
    await Future.delayed(const Duration(milliseconds: 800));
    
    final index = _dummyTransactions.indexWhere((t) => t.id == transactionId);
    if (index != -1) {
      _dummyTransactions[index] = _dummyTransactions[index].copyWith(
        status: TransactionStatus.completed,
        processedAt: DateTime.now(),
      );
      return _dummyTransactions[index];
    }
    
    throw Exception('Transaction not found');
  }
}
