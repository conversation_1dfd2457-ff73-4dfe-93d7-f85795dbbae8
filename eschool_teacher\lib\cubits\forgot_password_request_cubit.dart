import 'package:equatable/equatable.dart';
import 'package:eschool_teacher/data/repositories/authRepository.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

abstract class ForgotPasswordRequestState extends Equatable {}

class ForgotPasswordRequestInitial extends ForgotPasswordRequestState {
  @override
  List<Object?> get props => [];
}

class ForgotPasswordRequestInProgress extends ForgotPasswordRequestState {
  @override
  List<Object?> get props => [];
}

class ForgotPasswordCodeSentSuccess extends ForgotPasswordRequestState {
  @override
  List<Object?> get props => [];
}

class ForgotPasswordRequestFailure extends ForgotPasswordRequestState {
  final String errorMessage;

  ForgotPasswordRequestFailure(this.errorMessage);

  @override
  List<Object?> get props => [errorMessage];
}

class VerificationInProgress extends ForgotPasswordRequestState {
  @override
  List<Object?> get props => [];
}

class VerificationSuccess extends ForgotPasswordRequestState {
  @override
  List<Object?> get props => [];
}

class VerificationFailure extends ForgotPasswordRequestState {
  final String errorMessage;

  VerificationFailure(this.errorMessage);

  @override
  List<Object?> get props => [errorMessage];
}

class PasswordResetInProgress extends ForgotPasswordRequestState {
  @override
  List<Object?> get props => [];
}

class PasswordResetSuccess extends ForgotPasswordRequestState {
  @override
  List<Object?> get props => [];
}

class PasswordResetFailure extends ForgotPasswordRequestState {
  final String errorMessage;

  PasswordResetFailure(this.errorMessage);

  @override
  List<Object?> get props => [errorMessage];
}

class ForgotPasswordRequestCubit extends Cubit<ForgotPasswordRequestState> {
  final AuthRepository _authRepository;

  ForgotPasswordRequestCubit(this._authRepository)
      : super(ForgotPasswordRequestInitial());

  // Step 1: Request password reset code - PREVIEW MODE
  Future<void> requestResetPasswordCode({
    required String email,
  }) async {
    emit(ForgotPasswordRequestInProgress());
    
    try {
      await _authRepository.requestPasswordReset(email);
      emit(ForgotPasswordCodeSentSuccess());
    } catch (e) {
      emit(ForgotPasswordRequestFailure(e.toString()));
    }
  }

  // Step 2: Verify the code - PREVIEW MODE
  Future<void> verifyCode({
    required String email,
    required String code,
  }) async {
    emit(VerificationInProgress());
    
    try {
      await _authRepository.verifyPasswordResetOTP(email, code);
      emit(VerificationSuccess());
    } catch (e) {
      emit(VerificationFailure(e.toString()));
    }
  }

  // Step 3: Reset password with the code - PREVIEW MODE
  Future<void> resetPassword({
    required String email,
    required String code,
    required String password,
    required String confirmPassword,
  }) async {
    emit(PasswordResetInProgress());
    
    try {
      await _authRepository.resetPasswordWithApi(
        email: email,
        code: code,
        password: password,
        passwordConfirmation: confirmPassword,
      );
      emit(PasswordResetSuccess());
    } catch (e) {
      emit(PasswordResetFailure(e.toString()));
    }
  }
}
