import 'package:eschool_teacher/data/models/lesson_booking.dart';
import 'package:eschool_teacher/data/models/subject.dart';
import 'package:eschool_teacher/data/models/subject_level.dart';
import 'package:eschool_teacher/data/models/education_stage.dart';

class LessonBookingRepository {
  // Dummy data for subjects
  static final List<Subject> _dummySubjects = [
    Subject(
      id: 1,
      title: 'اللغة العربية',
      imageUrl: 'assets/images/arabic.svg',
      subjectLevels: [
        SubjectLevel(id: 1, name: 'صف 10'),
        SubjectLevel(id: 2, name: 'صف 11'),
        SubjectLevel(id: 3, name: 'صف 12'),
      ],
    ),
    Subject(
      id: 2,
      title: 'الرياضيات',
      imageUrl: 'assets/images/math.svg',
      subjectLevels: [
        SubjectLevel(id: 1, name: 'صف 10'),
        SubjectLevel(id: 2, name: 'صف 11'),
        SubjectLevel(id: 3, name: 'صف 12'),
      ],
    ),
    Subject(
      id: 3,
      title: 'الفيزياء',
      imageUrl: 'assets/images/physics.svg',
      subjectLevels: [
        SubjectLevel(id: 1, name: 'صف 11'),
        SubjectLevel(id: 2, name: 'صف 12'),
      ],
    ),
  ];

  static final List<EducationStage> _dummyEducationStages = [
    EducationStage(id: 1, name: 'المرحلة الثانوية', description: 'الصفوف 10-12'),
    EducationStage(id: 2, name: 'المرحلة الإعدادية', description: 'الصفوف 7-9'),
  ];

  // Generate dummy lesson bookings
  static List<LessonBooking> _generateDummyLessons() {
    final now = DateTime.now();
    final lessons = <LessonBooking>[];

    // Today's lessons
    lessons.add(LessonBooking(
      id: '1',
      title: 'درس اللغة العربية - الوحدة الأولى',
      description: 'مراجعة قواعد النحو والصرف',
      lessonType: LessonType.individual,
      status: LessonStatus.scheduled,
      subject: _dummySubjects[0],
      educationStage: _dummyEducationStages[0],
      lessonDate: now,
      startTime: DateTime(now.year, now.month, now.day, 11, 30),
      endTime: DateTime(now.year, now.month, now.day, 12, 30),
      teacherId: 'teacher_1',
      studentIds: ['student_1'],
      zoomLink: 'https://zoom.us/j/123456789',
      meetingId: '123 456 789',
      meetingPassword: 'abc123',
      pricePerStudent: 150.0,
      totalEarnings: 150.0,
      createdAt: now.subtract(const Duration(days: 2)),
    ));

    lessons.add(LessonBooking(
      id: '2',
      title: 'درس الرياضيات - الجبر',
      description: 'حل المعادلات الخطية',
      lessonType: LessonType.group,
      status: LessonStatus.scheduled,
      subject: _dummySubjects[1],
      educationStage: _dummyEducationStages[0],
      lessonDate: now,
      startTime: DateTime(now.year, now.month, now.day, 14, 0),
      endTime: DateTime(now.year, now.month, now.day, 15, 0),
      teacherId: 'teacher_1',
      studentIds: ['student_2', 'student_3'],
      zoomLink: 'https://zoom.us/j/987654321',
      meetingId: '987 654 321',
      meetingPassword: 'xyz789',
      pricePerStudent: 120.0,
      totalEarnings: 240.0,
      createdAt: now.subtract(const Duration(days: 1)),
    ));

    // Tomorrow's lessons
    final tomorrow = now.add(const Duration(days: 1));
    lessons.add(LessonBooking(
      id: '3',
      title: 'درس الفيزياء - الحركة',
      description: 'قوانين نيوتن للحركة',
      lessonType: LessonType.individual,
      status: LessonStatus.scheduled,
      subject: _dummySubjects[2],
      educationStage: _dummyEducationStages[0],
      lessonDate: tomorrow,
      startTime: DateTime(tomorrow.year, tomorrow.month, tomorrow.day, 10, 0),
      endTime: DateTime(tomorrow.year, tomorrow.month, tomorrow.day, 11, 0),
      teacherId: 'teacher_1',
      studentIds: ['student_4'],
      zoomLink: 'https://zoom.us/j/456789123',
      meetingId: '456 789 123',
      meetingPassword: 'def456',
      pricePerStudent: 180.0,
      totalEarnings: 180.0,
      createdAt: now.subtract(const Duration(hours: 12)),
    ));

    // Past lessons
    final yesterday = now.subtract(const Duration(days: 1));
    lessons.add(LessonBooking(
      id: '4',
      title: 'درس اللغة العربية - الأدب',
      description: 'دراسة النصوص الأدبية',
      lessonType: LessonType.individual,
      status: LessonStatus.completed,
      subject: _dummySubjects[0],
      educationStage: _dummyEducationStages[0],
      lessonDate: yesterday,
      startTime: DateTime(yesterday.year, yesterday.month, yesterday.day, 16, 0),
      endTime: DateTime(yesterday.year, yesterday.month, yesterday.day, 17, 0),
      teacherId: 'teacher_1',
      studentIds: ['student_1'],
      zoomLink: 'https://zoom.us/j/789123456',
      meetingId: '789 123 456',
      meetingPassword: 'ghi789',
      pricePerStudent: 150.0,
      totalEarnings: 150.0,
      createdAt: now.subtract(const Duration(days: 3)),
    ));

    return lessons;
  }

  static List<LessonBooking> _dummyLessons = _generateDummyLessons();

  // Get all lesson bookings
  Future<List<LessonBooking>> getAllLessons() async {
    await Future.delayed(const Duration(milliseconds: 500)); // Simulate network delay
    return List.from(_dummyLessons);
  }

  // Get lessons for today
  Future<List<LessonBooking>> getTodaysLessons() async {
    await Future.delayed(const Duration(milliseconds: 300));
    final today = DateTime.now();
    return _dummyLessons.where((lesson) {
      return lesson.lessonDate.year == today.year &&
             lesson.lessonDate.month == today.month &&
             lesson.lessonDate.day == today.day;
    }).toList();
  }

  // Get upcoming lessons
  Future<List<LessonBooking>> getUpcomingLessons() async {
    await Future.delayed(const Duration(milliseconds: 300));
    final now = DateTime.now();
    return _dummyLessons.where((lesson) {
      return lesson.lessonDate.isAfter(now) || 
             (lesson.isToday && lesson.startTime.isAfter(now));
    }).toList();
  }

  // Get completed lessons
  Future<List<LessonBooking>> getCompletedLessons() async {
    await Future.delayed(const Duration(milliseconds: 300));
    return _dummyLessons.where((lesson) {
      return lesson.status == LessonStatus.completed;
    }).toList();
  }

  // Get lesson by ID
  Future<LessonBooking?> getLessonById(String id) async {
    await Future.delayed(const Duration(milliseconds: 200));
    try {
      return _dummyLessons.firstWhere((lesson) => lesson.id == id);
    } catch (e) {
      return null;
    }
  }

  // Create new lesson booking
  Future<LessonBooking> createLesson(LessonBooking lesson) async {
    await Future.delayed(const Duration(milliseconds: 800)); // Simulate network delay
    final newLesson = lesson.copyWith(
      id: 'lesson_${DateTime.now().millisecondsSinceEpoch}',
      createdAt: DateTime.now(),
    );
    _dummyLessons.add(newLesson);
    return newLesson;
  }

  // Update lesson booking
  Future<LessonBooking> updateLesson(LessonBooking lesson) async {
    await Future.delayed(const Duration(milliseconds: 600));
    final index = _dummyLessons.indexWhere((l) => l.id == lesson.id);
    if (index != -1) {
      _dummyLessons[index] = lesson.copyWith(updatedAt: DateTime.now());
      return _dummyLessons[index];
    }
    throw Exception('Lesson not found');
  }

  // Cancel lesson
  Future<LessonBooking> cancelLesson(String lessonId) async {
    await Future.delayed(const Duration(milliseconds: 400));
    final index = _dummyLessons.indexWhere((l) => l.id == lessonId);
    if (index != -1) {
      _dummyLessons[index] = _dummyLessons[index].copyWith(
        status: LessonStatus.cancelled,
        updatedAt: DateTime.now(),
      );
      return _dummyLessons[index];
    }
    throw Exception('Lesson not found');
  }

  // Get lessons by date range
  Future<List<LessonBooking>> getLessonsByDateRange(DateTime start, DateTime end) async {
    await Future.delayed(const Duration(milliseconds: 400));
    return _dummyLessons.where((lesson) {
      return lesson.lessonDate.isAfter(start.subtract(const Duration(days: 1))) &&
             lesson.lessonDate.isBefore(end.add(const Duration(days: 1)));
    }).toList();
  }

  // Get lessons by subject
  Future<List<LessonBooking>> getLessonsBySubject(int subjectId) async {
    await Future.delayed(const Duration(milliseconds: 300));
    return _dummyLessons.where((lesson) {
      return lesson.subject.id == subjectId;
    }).toList();
  }

  // Get lessons by status
  Future<List<LessonBooking>> getLessonsByStatus(LessonStatus status) async {
    await Future.delayed(const Duration(milliseconds: 300));
    return _dummyLessons.where((lesson) {
      return lesson.status == status;
    }).toList();
  }
}
