/// ESchool Teacher App Design System
/// 
/// A comprehensive design system adapted from the student app
/// that provides consistent colors, typography, spacing, components, and themes
/// for the teacher application.
/// 
/// ## Usage
/// 
/// ### Basic Usage
/// ```dart
/// import 'package:eschool_teacher/ui/design_system/design_system.dart';
/// 
/// // Use design tokens
/// Container(
///   color: AppColors.primaryColor,
///   padding: EdgeInsets.all(AppSpacing.medium),
///   child: Text(
///     'مرحباً بك',
///     style: AppTypography.bodyMedium,
///   ),
/// )
/// ```
/// 
/// ### Using Components
/// ```dart
/// // Buttons
/// AppPrimaryButton(
///   text: 'إرسال',
///   onPressed: () {},
/// )
/// 
/// // Text Fields
/// AppTextField(
///   labelText: 'البريد الإلكتروني',
///   hintText: 'أدخل بريدك الإلكتروني',
/// )
/// 
/// // Bottom Sheets
/// AppModalBottomSheet.show(
///   context: context,
///   title: 'الإعدادات',
///   child: SettingsForm(),
/// )
/// ```
/// 
/// ### Using Theme
/// ```dart
/// MaterialApp(
///   theme: AppTheme.lightTheme,
///   darkTheme: AppTheme.darkTheme,
///   home: MyApp(),
/// )
/// ```
/// 
/// ## Features
/// 
/// - **Consistent Colors**: Primary, secondary, surface, and status colors
/// - **Typography System**: Poppins font family with Arabic support
/// - **Spacing System**: Uniform spacing values and padding patterns
/// - **Component Library**: Buttons, text fields, bottom sheets, cards, and more
/// - **Theme Configuration**: Complete Material Design 3 theme setup
/// - **RTL Support**: Full Arabic language and RTL layout support
/// - **Teacher-Specific**: Adapted for online tutoring use cases
/// 
/// ## Design Tokens
/// 
/// ### Colors
/// - Primary: #22577A (Blue)
/// - Secondary: #212121 (Dark Gray)
/// - Surface: #F6F6F6 (Light Gray)
/// - Background: #FFFFFF (White)
/// - Error: #FF6769 (Red)
/// - Status colors for success, warning, info states
/// 
/// ### Typography
/// - Font Family: Poppins (Google Fonts)
/// - Font Weights: 300, 400, 500, 600, 700
/// - Font Sizes: 10px - 24px
/// - Line Heights: 1.2 - 1.5
/// - Arabic RTL Support
/// 
/// ### Spacing
/// - Base Unit: 4px
/// - Scale: 4px, 8px, 12px, 16px, 20px, 24px, 32px
/// - Common values: 5px, 10px, 15px, 25px, 30px
/// 
/// ### Border Radius
/// - Small: 4px
/// - Medium: 8px
/// - Large: 10px
/// - XLarge: 15px
/// - XXLarge: 20px
/// 
/// ## Components
/// 
/// ### Buttons
/// - AppPrimaryButton: Main action button
/// - AppSecondaryButton: Secondary action button
/// - AppCustomRoundedButton: Custom styled button
/// - AppCloseButton: Close/dismiss button
/// - AppTextButton: Text-only button
/// - AppIconButton: Icon button
/// 
/// ### Text Fields
/// - AppTextField: Standard text input
/// - AppSearchTextField: Search input with icon
/// - AppBottomSheetTextField: Text field for bottom sheets
/// - AppDropdownTextField: Dropdown selection
/// 
/// ### Bottom Sheets
/// - AppModalBottomSheet: Base modal bottom sheet
/// - AppFormBottomSheet: Form container bottom sheet
/// - AppListSelectionBottomSheet: List selection sheet
/// 
/// ### Other Components
/// - AppCard: Container with elevation
/// - AppSurface: Flat surface container
/// - AppNoDataContainer: Empty state container
/// - AppLoadingContainer: Loading shimmer
/// - AppErrorContainer: Error state container
/// - AppStatusBadge: Status indicator
/// - AppListTile: List item component
/// - AppDivider: Consistent divider
/// - AppAvatar: User avatar component
/// 
/// ## File Structure
/// 
/// ```
/// eschool_teacher/lib/ui/design_system/
/// ├── design_system.dart              # Main export file
/// ├── app_design_system.dart          # Core design tokens
/// ├── app_theme.dart                  # Flutter theme configuration
/// └── components/
///     ├── app_buttons.dart            # Button components
///     ├── app_text_fields.dart        # Text field components
///     ├── app_bottom_sheets.dart      # Bottom sheet components
///     └── app_components.dart         # Other UI components
/// ```

library design_system;

// Core design system
export 'app_design_system.dart';
export 'app_theme.dart';

// Component library
export 'components/app_buttons.dart';
export 'components/app_text_fields.dart';
export 'components/app_bottom_sheets.dart';
export 'components/app_components.dart';
