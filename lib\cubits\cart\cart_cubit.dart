import 'package:bloc/bloc.dart';
import 'package:eschool/cubits/cart/cart_state.dart';
import 'package:eschool/data/models/cart/cart_item.dart';
import 'package:eschool/domain/repositories/cart_repository_interface.dart';
import 'package:flutter/foundation.dart';

class CartCubit extends Cubit<CartState> {
  final CartRepositoryInterface _cartRepository;

  CartCubit({
    required CartRepositoryInterface cartRepository,
  })  : _cartRepository = cartRepository,
        super(const CartInitial());

  /// Load the cart from local storage
  Future<void> loadCart() async {
    try {
      emit(const CartLoading());
      final cart = await _cartRepository.getCart();
      emit(CartLoaded(cart: cart));
    } catch (e) {
      debugPrint('Error loading cart: $e');
      emit(CartError(message: 'Failed to load cart: $e'));
    }
  }

  /// Add an item to the cart
  Future<void> addItem(CartItem item) async {
    try {
      emit(const CartLoading());
      final updatedCart = await _cartRepository.addItem(item);
      emit(CartLoaded(cart: updatedCart));
    } catch (e) {
      debugPrint('Error adding item to cart: $e');
      emit(CartError(message: 'Failed to add item to cart: $e'));
    }
  }

  /// Remove an item from the cart
  Future<void> removeItem(String itemId) async {
    try {
      emit(const CartLoading());
      final updatedCart = await _cartRepository.removeItem(itemId);
      emit(CartLoaded(cart: updatedCart));
    } catch (e) {
      debugPrint('Error removing item from cart: $e');
      emit(CartError(message: 'Failed to remove item from cart: $e'));
    }
  }

  /// Update the quantity of an item in the cart
  Future<void> updateItemQuantity(String itemId, int quantity) async {
    try {
      if (quantity <= 0) {
        await removeItem(itemId);
        return;
      }

      emit(const CartLoading());
      final updatedCart = await _cartRepository.updateItemQuantity(itemId, quantity);
      emit(CartLoaded(cart: updatedCart));
    } catch (e) {
      debugPrint('Error updating item quantity: $e');
      emit(CartError(message: 'Failed to update item quantity: $e'));
    }
  }

  /// Clear all items from the cart
  Future<void> clearCart() async {
    try {
      emit(const CartLoading());
      final updatedCart = await _cartRepository.clearCart();
      emit(CartLoaded(cart: updatedCart));
    } catch (e) {
      debugPrint('Error clearing cart: $e');
      emit(CartError(message: 'Failed to clear cart: $e'));
    }
  }

  /// Check if the cart contains an item
  Future<bool> containsItem(String itemId) async {
    return await _cartRepository.containsItem(itemId);
  }

  /// Toggle the selection state of an item in the cart
  Future<void> toggleItemSelection(String itemId) async {
    try {
      if (state is CartLoaded) {
        final currentCart = (state as CartLoaded).cart;
        final updatedCart = currentCart.toggleItemSelection(itemId);
        await _cartRepository.saveCart(updatedCart);
        emit(CartLoaded(cart: updatedCart));
      }
    } catch (e) {
      debugPrint('Error toggling item selection: $e');
      emit(CartError(message: 'Failed to toggle item selection: $e'));
    }
  }

  /// Select all items in the cart
  Future<void> selectAllItems() async {
    try {
      if (state is CartLoaded) {
        final currentCart = (state as CartLoaded).cart;
        final updatedCart = currentCart.selectAllItems();
        await _cartRepository.saveCart(updatedCart);
        emit(CartLoaded(cart: updatedCart));
      }
    } catch (e) {
      debugPrint('Error selecting all items: $e');
      emit(CartError(message: 'Failed to select all items: $e'));
    }
  }

  /// Deselect all items in the cart
  Future<void> deselectAllItems() async {
    try {
      if (state is CartLoaded) {
        final currentCart = (state as CartLoaded).cart;
        final updatedCart = currentCart.deselectAllItems();
        await _cartRepository.saveCart(updatedCart);
        emit(CartLoaded(cart: updatedCart));
      }
    } catch (e) {
      debugPrint('Error deselecting all items: $e');
      emit(CartError(message: 'Failed to deselect all items: $e'));
    }
  }
}
