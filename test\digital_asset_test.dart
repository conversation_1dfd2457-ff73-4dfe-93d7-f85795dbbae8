import 'package:flutter_test/flutter_test.dart';
import 'package:eschool/data/models/learning_resource.dart';
import 'package:eschool/data/models/subject.dart';
import 'package:eschool/data/models/package.dart';
import 'package:eschool/data/repositories/digitalAssetRepository.dart';

void main() {
  group('LearningResource Model Tests', () {
    test('LearningResource creation with all required fields', () {
      final subject = Subject(
        id: 1,
        title: 'Arabic',
        imageUrl: 'https://example.com/image.jpg',
      );

      final educationStage = EducationStage(id: 1, name: 'Primary');

      final resource = LearningResource(
        id: 1,
        title: 'Arabic Digital Asset',
        description: 'Learn Arabic fundamentals',
        price: 50.0,
        type: 'digital_asset',
        subject: subject,
        educationStage: educationStage,
      );

      expect(resource.id, 1);
      expect(resource.title, 'Arabic Digital Asset');
      expect(resource.description, 'Learn Arabic fundamentals');
      expect(resource.price, 50.0);
      expect(resource.type, 'digital_asset');
      expect(resource.subject?.id, 1);
      expect(resource.educationStage?.id, 1);
    });

    test('LearningResource JSON serialization and deserialization', () {
      final originalResource = LearningResource(
        id: 5,
        title: 'Arabic',
        description: 'Arabic',
        price: 50.0,
        type: 'digital_asset',
        subject: Subject(
          id: 1,
          title: 'Arabic',
          imageUrl: '',
        ),
        educationStage: EducationStage(id: 1, name: 'Primary'),
      );

      // Convert to JSON
      final json = originalResource.toJson();

      // Convert back from JSON
      final resourceFromJson = LearningResource.fromJson(json);

      expect(resourceFromJson.id, originalResource.id);
      expect(resourceFromJson.title, originalResource.title);
      expect(resourceFromJson.description, originalResource.description);
      expect(resourceFromJson.price, originalResource.price);
      expect(resourceFromJson.type, originalResource.type);
      expect(resourceFromJson.subject?.id, originalResource.subject?.id);
      expect(resourceFromJson.educationStage?.id, originalResource.educationStage?.id);
    });

    test('LearningResource fromJson handles API response format', () {
      // Simulate API response format
      final apiResponse = {
        "id": 5,
        "name": "Arabic", // API uses 'name' instead of 'title'
        "description": "Arabic",
        "type": "digital_asset",
        "price": "50.00", // API returns price as string
        "subject": {
          "id": 1,
          "name": "Arabic"
        },
        "education_stage": {
          "id": 1,
          "name": "Primary"
        }
      };

      final resource = LearningResource.fromJson(apiResponse);

      expect(resource.id, 5);
      expect(resource.title, 'Arabic'); // Should map from 'name'
      expect(resource.description, 'Arabic');
      expect(resource.price, 50.0); // Should parse string to double
      expect(resource.type, 'digital_asset');
      expect(resource.subject?.id, 1);
      expect(resource.educationStage?.id, 1);
      expect(resource.level, 'Primary'); // Should map from education_stage.name
    });

    test('LearningResource with null optional fields', () {
      final resource = LearningResource(
        id: 1,
        title: 'Basic Resource',
        description: 'Basic resource description',
        price: 25.0,
        type: 'digital_asset',
        subject: null,
        educationStage: null,
      );

      expect(resource.subject, isNull);
      expect(resource.educationStage, isNull);
      expect(resource.level, ''); // Should default to empty string
    });
  });

  group('DigitalAssetRepository Tests', () {
    test('getDummyDigitalAssets returns valid resources', () {
      final repository = DigitalAssetRepository();
      final resources = repository.getDummyDigitalAssets();

      expect(resources, isNotEmpty);
      expect(resources.length, 5);

      // Check first resource
      final firstResource = resources.first;
      expect(firstResource.id, 1);
      expect(firstResource.title, 'قواعد اللغة العربية - المستوى التأسيسي');
      expect(firstResource.type, 'digital_asset');
      expect(firstResource.subject?.id, 1);
    });

    test('fetchDigitalAssets method signature accepts correct parameters', () {
      final repository = DigitalAssetRepository();
      
      // This test verifies that the method signature is correct
      // In a real test, you would mock the API response
      expect(() => repository.fetchDigitalAssets(subjectId: 1, educationStageId: 2), 
             returnsNormally);
    });
  });
}
