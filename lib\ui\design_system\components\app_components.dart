import 'package:flutter/material.dart';
import '../app_design_system.dart';

// Export the individual classes for direct access
export '../app_design_system.dart' show AppColors, AppTypography, AppSpacing, AppBorderRadius, AppShadows, AppComponents;
export 'app_buttons.dart' show AppPrimaryButton, AppSecondaryButton, AppCustomRoundedButton, AppGoogleAuthButton, AppTextButton, AppIconButton, AppCloseButton;
export 'app_text_fields.dart';
export 'app_bottom_sheets.dart';

/// Reusable UI components extracted from the existing app design
/// 
/// This library provides consistent component styling that matches the current
/// app appearance while being reusable across multiple applications.

/// App card component with consistent styling
class AppCard extends StatelessWidget {  const AppCard({
    super.key,
    required this.child,
    this.margin,
    this.padding,
    this.backgroundColor = AppColors.pageBackgroundColor,
    this.borderRadius,
    this.elevation = 2,
    this.border,
    this.onTap,
  });

  final Widget child;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? padding;
  final Color backgroundColor;
  final BorderRadius? borderRadius;
  final double elevation;
  final BoxBorder? border;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      child: Material(
        color: backgroundColor,
        elevation: elevation,
        borderRadius: borderRadius ?? AppBorderRadius.card,
        child: InkWell(
          onTap: onTap,
          borderRadius: borderRadius ?? AppBorderRadius.card,
          child: Container(
            padding: padding ?? AppSpacing.cardPadding,
            decoration: BoxDecoration(
              border: border,
              borderRadius: borderRadius ?? AppBorderRadius.card,
            ),
            child: child,
          ),
        ),
      ),
    );
  }
}

/// Surface container with consistent styling
class AppSurface extends StatelessWidget {
  const AppSurface({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.backgroundColor = AppColors.surfaceColor,
    this.borderRadius,
    this.border,
    this.onTap,
  });

  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final Color backgroundColor;
  final BorderRadius? borderRadius;
  final BoxBorder? border;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: borderRadius ?? AppBorderRadius.roundedLarge,
        border: border,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: borderRadius ?? AppBorderRadius.roundedLarge,
          child: Container(
            padding: padding ?? AppSpacing.paddingM,
            child: child,
          ),
        ),
      ),
    );
  }
}

/// No data container component based on existing pattern
class AppNoDataContainer extends StatelessWidget {
  const AppNoDataContainer({
    super.key,
    this.title = 'No Data Found',
    this.subtitle,
    this.icon = Icons.inbox_outlined,
    this.iconSize = AppComponents.iconXLarge,
    this.actionButtonText,
    this.onActionPressed,
    this.backgroundColor = AppColors.pageBackgroundColor,
  });

  final String title;
  final String? subtitle;
  final IconData icon;
  final double iconSize;
  final String? actionButtonText;
  final VoidCallback? onActionPressed;
  final Color backgroundColor;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppSpacing.xLarge),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: AppBorderRadius.card,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: iconSize,
            color: AppColors.onSurfaceColor.withOpacity(0.5),
          ),
          const SizedBox(height: AppSpacing.medium),
          Text(
            title,
            style: AppTypography.heading3.copyWith(
              color: AppColors.onSurfaceColor,
            ),
            textAlign: TextAlign.center,
          ),
          if (subtitle != null) ...[
            const SizedBox(height: AppSpacing.xSmall),
            Text(
              subtitle!,
              style: AppTypography.bodyMedium.copyWith(
                color: AppColors.onSurfaceColor.withOpacity(0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
          if (actionButtonText != null && onActionPressed != null) ...[
            const SizedBox(height: AppSpacing.large),
            ElevatedButton(
              onPressed: onActionPressed,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryColor,
                foregroundColor: AppColors.onPrimaryColor,
                shape: RoundedRectangleBorder(
                  borderRadius: AppBorderRadius.button,
                ),
                padding: const EdgeInsets.symmetric(
                  horizontal: AppSpacing.xLarge,
                  vertical: AppSpacing.medium,
                ),
              ),
              child: Text(
                actionButtonText!,
                style: AppTypography.buttonMedium,
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// Loading container with shimmer effect
class AppLoadingContainer extends StatelessWidget {
  const AppLoadingContainer({
    super.key,
    this.width,
    this.height = 20,
    this.borderRadius,
    this.margin,
  });

  final double? width;
  final double height;
  final BorderRadius? borderRadius;
  final EdgeInsetsGeometry? margin;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      margin: margin,
      decoration: BoxDecoration(
        color: AppColors.shimmerBaseColor,
        borderRadius: borderRadius ?? BorderRadius.circular(4),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: borderRadius ?? BorderRadius.circular(4),
          gradient: LinearGradient(
            colors: [
              AppColors.shimmerBaseColor,
              AppColors.shimmerHighlightColor,
              AppColors.shimmerBaseColor,
            ],
            stops: const [0.1, 0.3, 0.4],
            begin: const Alignment(-1.0, -0.3),
            end: const Alignment(1.0, 0.3),
          ),
        ),
      ),
    );
  }
}

/// Error container component
class AppErrorContainer extends StatelessWidget {
  const AppErrorContainer({
    super.key,
    required this.message,
    this.title = 'Error',
    this.icon = Icons.error_outline,
    this.actionButtonText = 'Retry',
    this.onActionPressed,
    this.backgroundColor = AppColors.pageBackgroundColor,
  });

  final String message;
  final String title;
  final IconData icon;
  final String actionButtonText;
  final VoidCallback? onActionPressed;
  final Color backgroundColor;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppSpacing.xLarge),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: AppBorderRadius.card,
        border: Border.all(
          color: AppColors.errorColor.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: AppComponents.iconXLarge,
            color: AppColors.errorColor,
          ),
          const SizedBox(height: AppSpacing.medium),
          Text(
            title,
            style: AppTypography.heading3.copyWith(
              color: AppColors.errorColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppSpacing.xSmall),
          Text(
            message,
            style: AppTypography.bodyMedium.copyWith(
              color: AppColors.onSurfaceColor,
            ),
            textAlign: TextAlign.center,
          ),
          if (onActionPressed != null) ...[
            const SizedBox(height: AppSpacing.large),
            ElevatedButton(
              onPressed: onActionPressed,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.errorColor,
                foregroundColor: AppColors.onPrimaryColor,
                shape: RoundedRectangleBorder(
                  borderRadius: AppBorderRadius.button,
                ),
                padding: const EdgeInsets.symmetric(
                  horizontal: AppSpacing.xLarge,
                  vertical: AppSpacing.medium,
                ),
              ),
              child: Text(
                actionButtonText,
                style: AppTypography.buttonMedium,
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// Status badge component
class AppStatusBadge extends StatelessWidget {
  const AppStatusBadge({
    super.key,
    required this.text,
    required this.status,
    this.size = AppStatusBadgeSize.medium,
  });

  final String text;
  final AppStatusBadgeType status;
  final AppStatusBadgeSize size;

  @override
  Widget build(BuildContext context) {
    Color backgroundColor;
    Color textColor;
    
    switch (status) {
      case AppStatusBadgeType.success:
        backgroundColor = AppColors.greenColor.withOpacity(0.1);
        textColor = AppColors.greenColor;
        break;
      case AppStatusBadgeType.error:
        backgroundColor = AppColors.errorColor.withOpacity(0.1);
        textColor = AppColors.errorColor;
        break;
      case AppStatusBadgeType.warning:
        backgroundColor = AppColors.orangeColor.withOpacity(0.1);
        textColor = AppColors.orangeColor;
        break;
      case AppStatusBadgeType.info:
        backgroundColor = AppColors.blueColor.withOpacity(0.1);
        textColor = AppColors.blueColor;
        break;
      case AppStatusBadgeType.neutral:
        backgroundColor = AppColors.onSurfaceColor.withOpacity(0.1);
        textColor = AppColors.onSurfaceColor;
        break;
    }

    TextStyle textStyle;
    EdgeInsetsGeometry padding;
    
    switch (size) {
      case AppStatusBadgeSize.small:
        textStyle = AppTypography.caption;
        padding = const EdgeInsets.symmetric(
          horizontal: AppSpacing.xSmall,
          vertical: AppSpacing.xxSmall,
        );
        break;
      case AppStatusBadgeSize.medium:
        textStyle = AppTypography.labelSmall;
        padding = const EdgeInsets.symmetric(
          horizontal: AppSpacing.small,
          vertical: AppSpacing.xxSmall,
        );
        break;
      case AppStatusBadgeSize.large:
        textStyle = AppTypography.labelMedium;
        padding = const EdgeInsets.symmetric(
          horizontal: AppSpacing.medium,
          vertical: AppSpacing.xSmall,
        );
        break;
    }

    return Container(
      padding: padding,
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: AppBorderRadius.roundedXLarge,
      ),
      child: Text(
        text,
        style: textStyle.copyWith(color: textColor),
      ),
    );
  }
}

enum AppStatusBadgeType {
  success,
  error,
  warning,
  info,
  neutral,
}

enum AppStatusBadgeSize {
  small,
  medium,
  large,
}

/// List tile component with consistent styling
class AppListTile extends StatelessWidget {
  const AppListTile({
    super.key,
    this.leading,
    this.title,
    this.subtitle,
    this.trailing,
    this.onTap,
    this.padding,
    this.backgroundColor = Colors.transparent,
    this.borderRadius,
  });

  final Widget? leading;
  final Widget? title;
  final Widget? subtitle;
  final Widget? trailing;
  final VoidCallback? onTap;
  final EdgeInsetsGeometry? padding;
  final Color backgroundColor;
  final BorderRadius? borderRadius;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: borderRadius,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: borderRadius,
          child: Container(
            padding: padding ?? const EdgeInsets.symmetric(
              horizontal: AppSpacing.medium,
              vertical: AppSpacing.small,
            ),
            child: Row(
              children: [
                if (leading != null) ...[
                  leading!,
                  const SizedBox(width: AppSpacing.medium),
                ],
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (title != null) title!,
                      if (subtitle != null) ...[
                        const SizedBox(height: AppSpacing.xxSmall),
                        subtitle!,
                      ],
                    ],
                  ),
                ),
                if (trailing != null) ...[
                  const SizedBox(width: AppSpacing.medium),
                  trailing!,
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// Divider component with consistent styling
class AppDivider extends StatelessWidget {
  const AppDivider({
    super.key,
    this.height = 1,
    this.thickness = 1,
    this.color = AppColors.borderColor,
    this.indent = 0,
    this.endIndent = 0,
  });

  final double height;
  final double thickness;
  final Color color;
  final double indent;
  final double endIndent;

  @override
  Widget build(BuildContext context) {
    return Divider(
      height: height,
      thickness: thickness,
      color: color,
      indent: indent,
      endIndent: endIndent,
    );
  }
}

/// Avatar component with consistent styling
class AppAvatar extends StatelessWidget {
  const AppAvatar({
    super.key,
    this.imageUrl,
    this.name,
    this.size = AppComponents.iconLarge,
    this.backgroundColor = AppColors.surfaceColor,
    this.textColor = AppColors.secondaryColor,
    this.icon,
    this.onTap,
  });

  final String? imageUrl;
  final String? name;
  final double size;
  final Color backgroundColor;
  final Color textColor;
  final IconData? icon;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    Widget child;
    
    if (imageUrl != null && imageUrl!.isNotEmpty) {
      child = CircleAvatar(
        radius: size / 2,
        backgroundImage: NetworkImage(imageUrl!),
        backgroundColor: backgroundColor,
        onBackgroundImageError: (exception, stackTrace) {},
        child: const SizedBox(),
      );
    } else if (name != null && name!.isNotEmpty) {
      child = CircleAvatar(
        radius: size / 2,
        backgroundColor: backgroundColor,
        child: Text(
          name!.substring(0, 1).toUpperCase(),
          style: AppTypography.heading3.copyWith(
            color: textColor,
            fontSize: size * 0.4,
          ),
        ),
      );
    } else {
      child = CircleAvatar(
        radius: size / 2,
        backgroundColor: backgroundColor,
        child: Icon(
          icon ?? Icons.person,
          size: size * 0.6,
          color: textColor,
        ),
      );
    }

    if (onTap != null) {
      return GestureDetector(
        onTap: onTap,
        child: child,
      );
    }

    return child;
  }
}
