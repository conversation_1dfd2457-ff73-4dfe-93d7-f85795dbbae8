import 'package:flutter/material.dart';
import 'package:eschool_teacher/ui/design_system/design_system.dart';
import 'package:eschool_teacher/ui/screens/home/<USER>/teacher_header_widget.dart';
import 'package:eschool_teacher/ui/screens/home/<USER>/announcement_banner_widget.dart';
import 'package:eschool_teacher/ui/screens/home/<USER>/assignments_grid_widget.dart';
import 'package:eschool_teacher/ui/screens/home/<USER>/booked_lessons_widget.dart';

/// New simplified home container for teacher app
/// Matches the design from the provided screenshot
class HomeContainer extends StatelessWidget {
  const HomeContainer({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppColors.pageBackgroundColor,
      child: Safe<PERSON>rea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppSpacing.medium),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Teacher Header
              const TeacherHeaderWidget(),
              
              const SizedBox(height: AppSpacing.large),
              
              // Announcement Banner
              const AnnouncementBannerWidget(),
              
              const SizedBox(height: AppSpacing.large),
              
              // Assignments Grid
              const AssignmentsGridWidget(),
              
              const SizedBox(height: AppSpacing.large),
              
              // Booked Lessons
              const BookedLessonsWidget(),
              
              const SizedBox(height: AppSpacing.xxLarge),
            ],
          ),
        ),
      ),
    );
  }
}
