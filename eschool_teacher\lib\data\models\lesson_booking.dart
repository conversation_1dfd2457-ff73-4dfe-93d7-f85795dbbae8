import 'package:eschool_teacher/data/models/subject.dart';
import 'package:eschool_teacher/data/models/education_stage.dart';

enum LessonType { individual, group }
enum LessonStatus { scheduled, ongoing, completed, cancelled }

class LessonBooking {
  final String id;
  final String title;
  final String description;
  final LessonType lessonType;
  final LessonStatus status;
  final Subject subject;
  final EducationStage? educationStage;
  final DateTime lessonDate;
  final DateTime startTime;
  final DateTime endTime;
  final String teacherId;
  final List<String> studentIds;
  final String? zoomLink;
  final String? meetingId;
  final String? meetingPassword;
  final double pricePerStudent;
  final double totalEarnings;
  final DateTime createdAt;
  final DateTime? updatedAt;

  LessonBooking({
    required this.id,
    required this.title,
    this.description = '',
    required this.lessonType,
    required this.status,
    required this.subject,
    this.educationStage,
    required this.lessonDate,
    required this.startTime,
    required this.endTime,
    required this.teacherId,
    required this.studentIds,
    this.zoomLink,
    this.meetingId,
    this.meetingPassword,
    required this.pricePerStudent,
    required this.totalEarnings,
    required this.createdAt,
    this.updatedAt,
  });

  // Helper getters
  Duration get duration => endTime.difference(startTime);
  int get studentCount => studentIds.length;
  bool get isUpcoming => lessonDate.isAfter(DateTime.now());
  bool get isToday => 
      lessonDate.year == DateTime.now().year &&
      lessonDate.month == DateTime.now().month &&
      lessonDate.day == DateTime.now().day;

  String get formattedTime {
    final start = "${startTime.hour.toString().padLeft(2, '0')}:${startTime.minute.toString().padLeft(2, '0')}";
    final end = "${endTime.hour.toString().padLeft(2, '0')}:${endTime.minute.toString().padLeft(2, '0')}";
    return "$start - $end";
  }

  String get formattedDate {
    final months = [
      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    final weekdays = [
      'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت', 'الأحد'
    ];
    
    final weekday = weekdays[lessonDate.weekday - 1];
    final day = lessonDate.day;
    final month = months[lessonDate.month - 1];
    
    return "$weekday $day $month";
  }

  // Copy constructor for immutability
  LessonBooking copyWith({
    String? id,
    String? title,
    String? description,
    LessonType? lessonType,
    LessonStatus? status,
    Subject? subject,
    EducationStage? educationStage,
    DateTime? lessonDate,
    DateTime? startTime,
    DateTime? endTime,
    String? teacherId,
    List<String>? studentIds,
    String? zoomLink,
    String? meetingId,
    String? meetingPassword,
    double? pricePerStudent,
    double? totalEarnings,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return LessonBooking(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      lessonType: lessonType ?? this.lessonType,
      status: status ?? this.status,
      subject: subject ?? this.subject,
      educationStage: educationStage ?? this.educationStage,
      lessonDate: lessonDate ?? this.lessonDate,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      teacherId: teacherId ?? this.teacherId,
      studentIds: studentIds ?? this.studentIds,
      zoomLink: zoomLink ?? this.zoomLink,
      meetingId: meetingId ?? this.meetingId,
      meetingPassword: meetingPassword ?? this.meetingPassword,
      pricePerStudent: pricePerStudent ?? this.pricePerStudent,
      totalEarnings: totalEarnings ?? this.totalEarnings,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  factory LessonBooking.fromJson(Map<String, dynamic> json) {
    return LessonBooking(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      lessonType: LessonType.values.firstWhere(
        (e) => e.toString().split('.').last == json['lesson_type'],
        orElse: () => LessonType.individual,
      ),
      status: LessonStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => LessonStatus.scheduled,
      ),
      subject: Subject.fromJson(json['subject'] ?? {}),
      educationStage: json['education_stage'] != null 
          ? EducationStage.fromJson(json['education_stage'])
          : null,
      lessonDate: DateTime.parse(json['lesson_date']),
      startTime: DateTime.parse(json['start_time']),
      endTime: DateTime.parse(json['end_time']),
      teacherId: json['teacher_id'] ?? '',
      studentIds: List<String>.from(json['student_ids'] ?? []),
      zoomLink: json['zoom_link'],
      meetingId: json['meeting_id'],
      meetingPassword: json['meeting_password'],
      pricePerStudent: (json['price_per_student'] ?? 0.0).toDouble(),
      totalEarnings: (json['total_earnings'] ?? 0.0).toDouble(),
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'lesson_type': lessonType.toString().split('.').last,
      'status': status.toString().split('.').last,
      'subject': subject.toJson(),
      'education_stage': educationStage?.toJson(),
      'lesson_date': lessonDate.toIso8601String(),
      'start_time': startTime.toIso8601String(),
      'end_time': endTime.toIso8601String(),
      'teacher_id': teacherId,
      'student_ids': studentIds,
      'zoom_link': zoomLink,
      'meeting_id': meetingId,
      'meeting_password': meetingPassword,
      'price_per_student': pricePerStudent,
      'total_earnings': totalEarnings,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LessonBooking && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'LessonBooking(id: $id, title: $title, lessonType: $lessonType, status: $status, lessonDate: $lessonDate)';
  }
}
