enum TransactionType { lesson, bonus, withdrawal, refund }
enum TransactionStatus { pending, completed, failed, cancelled }

class EarningsTransaction {
  final String id;
  final String teacherId;
  final TransactionType type;
  final TransactionStatus status;
  final double amount;
  final String currency;
  final String description;
  final String? lessonId;
  final DateTime createdAt;
  final DateTime? processedAt;

  EarningsTransaction({
    required this.id,
    required this.teacherId,
    required this.type,
    required this.status,
    required this.amount,
    this.currency = 'QAR',
    required this.description,
    this.lessonId,
    required this.createdAt,
    this.processedAt,
  });

  // Helper getters
  bool get isPending => status == TransactionStatus.pending;
  bool get isCompleted => status == TransactionStatus.completed;
  bool get isIncome => type == TransactionType.lesson || type == TransactionType.bonus;
  bool get isOutgoing => type == TransactionType.withdrawal;

  String get formattedAmount {
    final sign = isOutgoing ? '-' : '+';
    return '$sign${amount.toStringAsFixed(2)} $currency';
  }

  String get formattedDate {
    final date = processedAt ?? createdAt;
    return '${date.day}/${date.month}/${date.year}';
  }

  String get typeDisplayName {
    switch (type) {
      case TransactionType.lesson:
        return 'درس';
      case TransactionType.bonus:
        return 'مكافأة';
      case TransactionType.withdrawal:
        return 'سحب';
      case TransactionType.refund:
        return 'استرداد';
    }
  }

  String get statusDisplayName {
    switch (status) {
      case TransactionStatus.pending:
        return 'قيد الانتظار';
      case TransactionStatus.completed:
        return 'مكتمل';
      case TransactionStatus.failed:
        return 'فشل';
      case TransactionStatus.cancelled:
        return 'ملغي';
    }
  }

  // Copy constructor for immutability
  EarningsTransaction copyWith({
    String? id,
    String? teacherId,
    TransactionType? type,
    TransactionStatus? status,
    double? amount,
    String? currency,
    String? description,
    String? lessonId,
    DateTime? createdAt,
    DateTime? processedAt,
  }) {
    return EarningsTransaction(
      id: id ?? this.id,
      teacherId: teacherId ?? this.teacherId,
      type: type ?? this.type,
      status: status ?? this.status,
      amount: amount ?? this.amount,
      currency: currency ?? this.currency,
      description: description ?? this.description,
      lessonId: lessonId ?? this.lessonId,
      createdAt: createdAt ?? this.createdAt,
      processedAt: processedAt ?? this.processedAt,
    );
  }

  factory EarningsTransaction.fromJson(Map<String, dynamic> json) {
    return EarningsTransaction(
      id: json['id'] ?? '',
      teacherId: json['teacher_id'] ?? '',
      type: TransactionType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
        orElse: () => TransactionType.lesson,
      ),
      status: TransactionStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => TransactionStatus.pending,
      ),
      amount: (json['amount'] ?? 0.0).toDouble(),
      currency: json['currency'] ?? 'QAR',
      description: json['description'] ?? '',
      lessonId: json['lesson_id'],
      createdAt: DateTime.parse(json['created_at']),
      processedAt: json['processed_at'] != null 
          ? DateTime.parse(json['processed_at'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'teacher_id': teacherId,
      'type': type.toString().split('.').last,
      'status': status.toString().split('.').last,
      'amount': amount,
      'currency': currency,
      'description': description,
      'lesson_id': lessonId,
      'created_at': createdAt.toIso8601String(),
      'processed_at': processedAt?.toIso8601String(),
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is EarningsTransaction && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'EarningsTransaction(id: $id, type: $type, amount: $amount, status: $status)';
  }
}

class EarningsSummary {
  final double totalEarnings;
  final double availableBalance;
  final double pendingEarnings;
  final double totalWithdrawn;
  final int totalLessonsGiven;
  final int lessonsThisMonth;
  final double earningsThisMonth;
  final DateTime lastUpdated;

  EarningsSummary({
    required this.totalEarnings,
    required this.availableBalance,
    required this.pendingEarnings,
    required this.totalWithdrawn,
    required this.totalLessonsGiven,
    required this.lessonsThisMonth,
    required this.earningsThisMonth,
    required this.lastUpdated,
  });

  // Helper getters
  String get formattedTotalEarnings => '${totalEarnings.toStringAsFixed(2)} ريال';
  String get formattedAvailableBalance => '${availableBalance.toStringAsFixed(2)} ريال';
  String get formattedPendingEarnings => '${pendingEarnings.toStringAsFixed(2)} ريال';
  String get formattedEarningsThisMonth => '${earningsThisMonth.toStringAsFixed(2)} ريال';

  double get averageEarningsPerLesson => 
      totalLessonsGiven > 0 ? totalEarnings / totalLessonsGiven : 0.0;

  // Copy constructor for immutability
  EarningsSummary copyWith({
    double? totalEarnings,
    double? availableBalance,
    double? pendingEarnings,
    double? totalWithdrawn,
    int? totalLessonsGiven,
    int? lessonsThisMonth,
    double? earningsThisMonth,
    DateTime? lastUpdated,
  }) {
    return EarningsSummary(
      totalEarnings: totalEarnings ?? this.totalEarnings,
      availableBalance: availableBalance ?? this.availableBalance,
      pendingEarnings: pendingEarnings ?? this.pendingEarnings,
      totalWithdrawn: totalWithdrawn ?? this.totalWithdrawn,
      totalLessonsGiven: totalLessonsGiven ?? this.totalLessonsGiven,
      lessonsThisMonth: lessonsThisMonth ?? this.lessonsThisMonth,
      earningsThisMonth: earningsThisMonth ?? this.earningsThisMonth,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  factory EarningsSummary.fromJson(Map<String, dynamic> json) {
    return EarningsSummary(
      totalEarnings: (json['total_earnings'] ?? 0.0).toDouble(),
      availableBalance: (json['available_balance'] ?? 0.0).toDouble(),
      pendingEarnings: (json['pending_earnings'] ?? 0.0).toDouble(),
      totalWithdrawn: (json['total_withdrawn'] ?? 0.0).toDouble(),
      totalLessonsGiven: json['total_lessons_given'] ?? 0,
      lessonsThisMonth: json['lessons_this_month'] ?? 0,
      earningsThisMonth: (json['earnings_this_month'] ?? 0.0).toDouble(),
      lastUpdated: DateTime.parse(json['last_updated']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total_earnings': totalEarnings,
      'available_balance': availableBalance,
      'pending_earnings': pendingEarnings,
      'total_withdrawn': totalWithdrawn,
      'total_lessons_given': totalLessonsGiven,
      'lessons_this_month': lessonsThisMonth,
      'earnings_this_month': earningsThisMonth,
      'last_updated': lastUpdated.toIso8601String(),
    };
  }

  @override
  String toString() {
    return 'EarningsSummary(totalEarnings: $totalEarnings, availableBalance: $availableBalance, totalLessonsGiven: $totalLessonsGiven)';
  }
}
