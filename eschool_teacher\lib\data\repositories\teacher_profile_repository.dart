import 'package:eschool_teacher/data/models/teacher_profile.dart';
import 'package:eschool_teacher/data/models/subject.dart';
import 'package:eschool_teacher/data/models/subject_level.dart';

class TeacherProfileRepository {
  // Generate dummy teacher profile
  static TeacherProfile _generateDummyProfile() {
    final arabicSubject = Subject(
      id: 1,
      title: 'اللغة العربية',
      imageUrl: 'assets/images/arabic.svg',
      subjectLevels: [
        SubjectLevel(id: 1, name: 'صف 10'),
        SubjectLevel(id: 2, name: 'صف 11'),
        SubjectLevel(id: 3, name: 'صف 12'),
      ],
    );

    final mathSubject = Subject(
      id: 2,
      title: 'الرياضيات',
      imageUrl: 'assets/images/math.svg',
      subjectLevels: [
        SubjectLevel(id: 1, name: 'صف 10'),
        SubjectLevel(id: 2, name: 'صف 11'),
      ],
    );

    final physicsSubject = Subject(
      id: 3,
      title: 'الفيزياء',
      imageUrl: 'assets/images/physics.svg',
      subjectLevels: [
        SubjectLevel(id: 2, name: 'صف 11'),
        SubjectLevel(id: 3, name: 'صف 12'),
      ],
    );

    return TeacherProfile(
      id: 'teacher_1',
      firstName: 'سعيد',
      lastName: 'أحمد المنصوري',
      email: '<EMAIL>',
      mobile: '+974 5555 1234',
      profileImageUrl: 'https://via.placeholder.com/150/4CAF50/FFFFFF?text=س',
      bio: 'مدرس لغة عربية متخصص مع خبرة 8 سنوات في التدريس. حاصل على ماجستير في اللغة العربية وآدابها من جامعة قطر. أسعى لتبسيط قواعد اللغة العربية وجعل تعلمها ممتعاً للطلاب.',
      qualification: 'ماجستير في اللغة العربية وآدابها',
      yearsOfExperience: 8,
      subjectsTaught: [
        SubjectTaught(
          subject: arabicSubject,
          levels: arabicSubject.subjectLevels,
          pricePerHour: 150.0,
        ),
        SubjectTaught(
          subject: mathSubject,
          levels: mathSubject.subjectLevels,
          pricePerHour: 120.0,
        ),
        SubjectTaught(
          subject: physicsSubject,
          levels: physicsSubject.subjectLevels,
          pricePerHour: 180.0,
        ),
      ],
      iban: '*****************************',
      bankName: 'بنك قطر الوطني',
      accountHolderName: 'سعيد أحمد المنصوري',
      weeklyLessonLimit: 25,
      lessonsGiven: 142,
      rating: 4.8,
      totalReviews: 89,
      isActive: true,
      isVerified: true,
      joinedAt: DateTime.now().subtract(const Duration(days: 365)),
      lastActiveAt: DateTime.now().subtract(const Duration(minutes: 15)),
    );
  }

  static TeacherProfile _dummyProfile = _generateDummyProfile();

  // Get teacher profile
  Future<TeacherProfile> getProfile() async {
    await Future.delayed(const Duration(milliseconds: 500)); // Simulate network delay
    return _dummyProfile;
  }

  // Update teacher profile
  Future<TeacherProfile> updateProfile(TeacherProfile updatedProfile) async {
    await Future.delayed(const Duration(milliseconds: 800)); // Simulate network delay
    _dummyProfile = updatedProfile;
    return _dummyProfile;
  }

  // Update basic info
  Future<TeacherProfile> updateBasicInfo({
    String? firstName,
    String? lastName,
    String? email,
    String? mobile,
    String? bio,
  }) async {
    await Future.delayed(const Duration(milliseconds: 600));
    
    _dummyProfile = _dummyProfile.copyWith(
      firstName: firstName ?? _dummyProfile.firstName,
      lastName: lastName ?? _dummyProfile.lastName,
      email: email ?? _dummyProfile.email,
      mobile: mobile ?? _dummyProfile.mobile,
      bio: bio ?? _dummyProfile.bio,
      lastActiveAt: DateTime.now(),
    );
    
    return _dummyProfile;
  }

  // Update qualification and experience
  Future<TeacherProfile> updateQualification({
    String? qualification,
    int? yearsOfExperience,
  }) async {
    await Future.delayed(const Duration(milliseconds: 600));
    
    _dummyProfile = _dummyProfile.copyWith(
      qualification: qualification ?? _dummyProfile.qualification,
      yearsOfExperience: yearsOfExperience ?? _dummyProfile.yearsOfExperience,
      lastActiveAt: DateTime.now(),
    );
    
    return _dummyProfile;
  }

  // Update banking information
  Future<TeacherProfile> updateBankingInfo({
    String? iban,
    String? bankName,
    String? accountHolderName,
  }) async {
    await Future.delayed(const Duration(milliseconds: 700));
    
    _dummyProfile = _dummyProfile.copyWith(
      iban: iban ?? _dummyProfile.iban,
      bankName: bankName ?? _dummyProfile.bankName,
      accountHolderName: accountHolderName ?? _dummyProfile.accountHolderName,
      lastActiveAt: DateTime.now(),
    );
    
    return _dummyProfile;
  }

  // Update subjects taught
  Future<TeacherProfile> updateSubjectsTaught(List<SubjectTaught> subjectsTaught) async {
    await Future.delayed(const Duration(milliseconds: 800));
    
    _dummyProfile = _dummyProfile.copyWith(
      subjectsTaught: subjectsTaught,
      lastActiveAt: DateTime.now(),
    );
    
    return _dummyProfile;
  }

  // Update weekly lesson limit
  Future<TeacherProfile> updateWeeklyLimit(int weeklyLimit) async {
    await Future.delayed(const Duration(milliseconds: 400));
    
    _dummyProfile = _dummyProfile.copyWith(
      weeklyLessonLimit: weeklyLimit,
      lastActiveAt: DateTime.now(),
    );
    
    return _dummyProfile;
  }

  // Update profile image
  Future<TeacherProfile> updateProfileImage(String imageUrl) async {
    await Future.delayed(const Duration(milliseconds: 1000)); // Simulate image upload
    
    _dummyProfile = _dummyProfile.copyWith(
      profileImageUrl: imageUrl,
      lastActiveAt: DateTime.now(),
    );
    
    return _dummyProfile;
  }

  // Toggle active status
  Future<TeacherProfile> toggleActiveStatus() async {
    await Future.delayed(const Duration(milliseconds: 400));
    
    _dummyProfile = _dummyProfile.copyWith(
      isActive: !_dummyProfile.isActive,
      lastActiveAt: DateTime.now(),
    );
    
    return _dummyProfile;
  }

  // Get available subjects (for adding new subjects)
  Future<List<Subject>> getAvailableSubjects() async {
    await Future.delayed(const Duration(milliseconds: 300));
    
    return [
      Subject(
        id: 1,
        title: 'اللغة العربية',
        imageUrl: 'assets/images/arabic.svg',
        subjectLevels: [
          SubjectLevel(id: 1, name: 'صف 10'),
          SubjectLevel(id: 2, name: 'صف 11'),
          SubjectLevel(id: 3, name: 'صف 12'),
        ],
      ),
      Subject(
        id: 2,
        title: 'الرياضيات',
        imageUrl: 'assets/images/math.svg',
        subjectLevels: [
          SubjectLevel(id: 1, name: 'صف 10'),
          SubjectLevel(id: 2, name: 'صف 11'),
          SubjectLevel(id: 3, name: 'صف 12'),
        ],
      ),
      Subject(
        id: 3,
        title: 'الفيزياء',
        imageUrl: 'assets/images/physics.svg',
        subjectLevels: [
          SubjectLevel(id: 2, name: 'صف 11'),
          SubjectLevel(id: 3, name: 'صف 12'),
        ],
      ),
      Subject(
        id: 4,
        title: 'الكيمياء',
        imageUrl: 'assets/images/chemistry.svg',
        subjectLevels: [
          SubjectLevel(id: 2, name: 'صف 11'),
          SubjectLevel(id: 3, name: 'صف 12'),
        ],
      ),
      Subject(
        id: 5,
        title: 'الأحياء',
        imageUrl: 'assets/images/biology.svg',
        subjectLevels: [
          SubjectLevel(id: 2, name: 'صف 11'),
          SubjectLevel(id: 3, name: 'صف 12'),
        ],
      ),
      Subject(
        id: 6,
        title: 'اللغة الإنجليزية',
        imageUrl: 'assets/images/english.svg',
        subjectLevels: [
          SubjectLevel(id: 1, name: 'صف 10'),
          SubjectLevel(id: 2, name: 'صف 11'),
          SubjectLevel(id: 3, name: 'صف 12'),
        ],
      ),
      Subject(
        id: 7,
        title: 'التاريخ',
        imageUrl: 'assets/images/history.svg',
        subjectLevels: [
          SubjectLevel(id: 1, name: 'صف 10'),
          SubjectLevel(id: 2, name: 'صف 11'),
          SubjectLevel(id: 3, name: 'صف 12'),
        ],
      ),
      Subject(
        id: 8,
        title: 'الجغرافيا',
        imageUrl: 'assets/images/geography.svg',
        subjectLevels: [
          SubjectLevel(id: 1, name: 'صف 10'),
          SubjectLevel(id: 2, name: 'صف 11'),
          SubjectLevel(id: 3, name: 'صف 12'),
        ],
      ),
    ];
  }

  // Get teacher statistics
  Future<Map<String, dynamic>> getTeacherStats() async {
    await Future.delayed(const Duration(milliseconds: 400));
    
    return {
      'total_lessons': _dummyProfile.lessonsGiven,
      'average_rating': _dummyProfile.rating,
      'total_reviews': _dummyProfile.totalReviews,
      'subjects_count': _dummyProfile.subjectsTaught.length,
      'years_experience': _dummyProfile.yearsOfExperience,
      'weekly_limit': _dummyProfile.weeklyLessonLimit,
      'is_verified': _dummyProfile.isVerified,
      'is_active': _dummyProfile.isActive,
      'joined_date': _dummyProfile.joinedAt.toIso8601String(),
      'last_active': _dummyProfile.lastActiveAt?.toIso8601String(),
    };
  }
}
