import 'package:eschool_teacher/data/models/subject_level.dart';

class Subject {
  final int id;
  final String title;
  final String imageUrl;
  final List<SubjectLevel> subjectLevels;

  // For backward compatibility with existing teacher app code
  String get name => title;
  String get image => imageUrl;
  String get code => "";
  String get bgColor => "";
  int get mediumId => 0;
  String get type => subjectLevels.isNotEmpty ? subjectLevels.first.name : "";

  // Additional backward compatibility getters
  bool get showType => subjectLevels.isNotEmpty;
  String get subjectNameWithType => "$title (${subjectLevels.isNotEmpty ? subjectLevels.first.name : ''})";

  // For compatibility with existing code
  bool get isSubjectImageSvg {
    final imageUrlParts = imageUrl.split(".");
    return imageUrlParts.isNotEmpty && imageUrlParts.last.toLowerCase() == "svg";
  }

  Subject({
    required this.id,
    required this.title,
    required this.imageUrl,
    this.subjectLevels = const [],
  });

  // Add copyWith method for immutability
  Subject copyWith({
    int? id,
    String? title,
    String? imageUrl,
    List<SubjectLevel>? subjectLevels,
  }) {
    return Subject(
      id: id ?? this.id,
      title: title ?? this.title,
      imageUrl: imageUrl ?? this.imageUrl,
      subjectLevels: subjectLevels ?? this.subjectLevels,
    );
  }

  factory Subject.fromJson(Map<String, dynamic> json) {
    // Handle both new and old JSON formats
    final id = json['id'] is String ? int.parse(json['id']) : json['id'] as int;
    final title = json['title'] ?? json['name'] ?? '';
    final imageUrl = json['imageUrl'] ?? json['image'] ?? '';

    // Handle subject levels
    List<SubjectLevel> levels = [];
    if (json['subjectLevels'] != null) {
      levels = (json['subjectLevels'] as List)
          .map((level) => SubjectLevel.fromJson(level))
          .toList();
    } else if (json['levels'] != null) {
      // Handle old format where levels were just strings
      levels = (json['levels'] as List)
          .asMap()
          .entries
          .map((entry) => SubjectLevel(
                id: entry.key + 1,
                name: entry.value.toString(),
              ))
          .toList();
    }

    return Subject(
      id: id,
      title: title,
      imageUrl: imageUrl,
      subjectLevels: levels,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'imageUrl': imageUrl,
      'subjectLevels': subjectLevels.map((level) => level.toJson()).toList(),
      // Include backward compatibility fields
      'name': title,
      'image': imageUrl,
      'type': subjectLevels.isNotEmpty ? subjectLevels.first.name : "",
      'levels': subjectLevels.map((level) => level.name).toList(),
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Subject && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Subject(id: $id, title: $title, imageUrl: $imageUrl, subjectLevels: ${subjectLevels.length})';
  }
}
