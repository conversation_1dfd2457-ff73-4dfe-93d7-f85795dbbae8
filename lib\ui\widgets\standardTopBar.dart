import 'package:eschool/ui/design_system/app_design_system.dart';
import 'package:eschool/ui/widgets/cart_icon_with_badge.dart';
import 'package:eschool/utils/uiUtils.dart';
import 'package:flutter/material.dart';

class StandardTopBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final bool centerTitle;
  final Color backgroundColor;
  final Color textColor;
  final bool showBackButton;
  final bool showCartIcon;
  final VoidCallback? onBackPressed;
  final List<Widget>? actions;
  final double elevation;
  final double height;
  final TextStyle? titleStyle;
  final bool automaticallyImplyLeading;

  const StandardTopBar({
    Key? key,
    required this.title,
    this.centerTitle = true,
    this.backgroundColor = AppColors.primaryColor,
    this.textColor = Colors.white,
    this.showBackButton = true,
    this.showCartIcon = false,
    this.onBackPressed,
    this.actions,
    this.elevation = 0,
    this.height = kToolbarHeight,
    this.titleStyle,
    this.automaticallyImplyLeading = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final effectiveTextStyle = titleStyle ?? TextStyle(
      color: textColor,
      fontWeight: FontWeight.bold,
      fontSize: 16,
    );

    // Build actions list
    final List<Widget> effectiveActions = [];
    
    // Add cart icon if needed
    if (showCartIcon) {
      effectiveActions.add(const CartIconWithBadge());
    }
    
    // Add any additional actions
    if (actions != null && actions!.isNotEmpty) {
      effectiveActions.addAll(actions!);
    }
    
    // Get current text direction
    final isRtl = Directionality.of(context) == TextDirection.rtl;

    return AppBar(
      title: Text(
        title,
        style: effectiveTextStyle,
        overflow: TextOverflow.ellipsis,
      ),
      centerTitle: centerTitle,
      backgroundColor: backgroundColor,
      elevation: elevation,
      automaticallyImplyLeading: automaticallyImplyLeading,
      iconTheme: IconThemeData(color: textColor),
      leading: showBackButton && automaticallyImplyLeading ? IconButton(
        icon: Transform.rotate(
          angle: isRtl ? 0:3.14159 , // Rotate 180 degrees (π radians) for RTL
          child: Icon(
            Icons.arrow_back,
            color: textColor,
          ),
        ),
        onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
      ) : null,
      actions: effectiveActions.isEmpty ? null : effectiveActions,
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(height);
} 