import 'dart:async';
import 'package:eschool/ui/design_system/app_design_system.dart';
import 'package:eschool/utils/labelKeys.dart';
import 'package:eschool/utils/uiUtils.dart';
import 'package:flutter/material.dart';

class CustomSearchTextField extends StatefulWidget {
  final TextEditingController textController;
  final Function(String) onSearch;
  final Function() onTextClear;
  const CustomSearchTextField(
      {super.key,
      required this.textController,
      required this.onSearch,
      required this.onTextClear,});

  @override
  State<CustomSearchTextField> createState() => _CustomSearchTextFieldState();
}

class _CustomSearchTextFieldState extends State<CustomSearchTextField> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  Timer? _debounce;

  @override
  void dispose() {
    _debounce?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: TextFormField(
        controller: widget.textController,
        autofocus: true,
        decoration: InputDecoration(
          hintStyle: const TextStyle(fontSize: 14),
          hintText: UiUtils.getTranslatedLabel(context, searchHintKey),
          fillColor: Theme.of(context).scaffoldBackgroundColor,
          filled: true,
          contentPadding: const EdgeInsets.all(5),
          prefixIcon: const Icon(
            Icons.search,
            color: AppColors.primaryColor,
          ),
          suffixIcon: IconButton(
            icon: const Icon(
              Icons.clear,
              color: AppColors.primaryColor,
            ),
            onPressed: () {
              if (_debounce?.isActive ?? false) _debounce?.cancel();
              widget.onTextClear();
              widget.textController.clear();
              _formKey.currentState!.validate();
            },
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(25.0),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(25.0),
            borderSide: const BorderSide(
              color: AppColors.redColor,
            ),
          ),
          errorStyle: TextStyle(
            color: Theme.of(context).scaffoldBackgroundColor,
          ),
        ),
        validator: (value) {
          if (value == null || value.trim().isEmpty) {
            return null;
          } else if (value.trim().length < 3) {
            return UiUtils.getTranslatedLabel(
                context, addMoreCharactorsToSearchKey,);
          } else {
            return null;
          }
        },
        onChanged: (value) {
          if (_debounce?.isActive ?? false) _debounce?.cancel();
          _debounce = Timer(const Duration(milliseconds: 500), () {
            if (_formKey.currentState!.validate()) {
              if (widget.textController.text.trim().isNotEmpty) {
                widget.onSearch(widget.textController.text);
              }
            } else {
              widget.onTextClear();
            }
          });
        },
      ),
    );
  }
}
