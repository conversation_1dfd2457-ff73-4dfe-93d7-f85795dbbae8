import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../app_design_system.dart';

/// Reusable text field components extracted from the existing app design
/// 
/// This library provides consistent text field styling that matches the current
/// app appearance while being reusable across multiple applications.

/// Standard text field component based on existing patterns
class AppTextField extends StatelessWidget {
  const AppTextField({
    super.key,
    this.controller,
    this.labelText,
    this.hintText,
    this.helperText,
    this.errorText,
    this.prefixIcon,
    this.suffixIcon,
    this.obscureText = false,
    this.enabled = true,
    this.readOnly = false,
    this.maxLines = 1,
    this.maxLength,
    this.keyboardType,
    this.textInputAction,
    this.onChanged,
    this.onSubmitted,
    this.onTap,
    this.validator,
    this.inputFormatters,
    this.focusNode,
    this.autofocus = false,
    this.textCapitalization = TextCapitalization.none,
  });

  final TextEditingController? controller;
  final String? labelText;
  final String? hintText;
  final String? helperText;
  final String? errorText;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final bool obscureText;
  final bool enabled;
  final bool readOnly;
  final int maxLines;
  final int? maxLength;
  final TextInputType? keyboardType;
  final TextInputAction? textInputAction;
  final ValueChanged<String>? onChanged;
  final ValueChanged<String>? onSubmitted;
  final VoidCallback? onTap;
  final String? Function(String?)? validator;
  final List<TextInputFormatter>? inputFormatters;
  final FocusNode? focusNode;
  final bool autofocus;
  final TextCapitalization textCapitalization;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (labelText != null) ...[
          Text(
            labelText!,
            style: AppTypography.labelMedium,
          ),
          const SizedBox(height: AppSpacing.xSmall),
        ],
        TextFormField(
          controller: controller,
          obscureText: obscureText,
          enabled: enabled,
          readOnly: readOnly,
          maxLines: maxLines,
          maxLength: maxLength,
          keyboardType: keyboardType,
          textInputAction: textInputAction,
          onChanged: onChanged,
          onFieldSubmitted: onSubmitted,
          onTap: onTap,
          validator: validator,
          inputFormatters: inputFormatters,
          focusNode: focusNode,
          autofocus: autofocus,
          textCapitalization: textCapitalization,
          style: AppTypography.bodyMedium,
          decoration: InputDecoration(
            hintText: hintText,
            hintStyle: AppTypography.hint,
            helperText: helperText,
            helperStyle: AppTypography.caption,
            errorText: errorText,
            errorStyle: AppTypography.error,
            prefixIcon: prefixIcon,
            suffixIcon: suffixIcon,
            filled: true,
            fillColor: AppColors.surfaceColor,
            border: OutlineInputBorder(
              borderRadius: AppBorderRadius.textField,
              borderSide: const BorderSide(
                color: AppColors.borderColor,
                width: AppComponents.textFieldBorderWidth,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: AppBorderRadius.textField,
              borderSide: const BorderSide(
                color: AppColors.borderColor,
                width: AppComponents.textFieldBorderWidth,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: AppBorderRadius.textField,
              borderSide: const BorderSide(
                color: AppColors.primaryColor,
                width: 2.0,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: AppBorderRadius.textField,
              borderSide: const BorderSide(
                color: AppColors.errorColor,
                width: AppComponents.textFieldBorderWidth,
              ),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: AppBorderRadius.textField,
              borderSide: const BorderSide(
                color: AppColors.errorColor,
                width: 2.0,
              ),
            ),
            disabledBorder: OutlineInputBorder(
              borderRadius: AppBorderRadius.textField,
              borderSide: const BorderSide(
                color: AppColors.disabledColor,
                width: AppComponents.textFieldBorderWidth,
              ),
            ),
            contentPadding: AppSpacing.textFieldPadding,
            counterStyle: AppTypography.caption,
          ),
        ),
      ],
    );
  }
}

/// Search text field component based on existing SearchTextField
class AppSearchTextField extends StatelessWidget {
  const AppSearchTextField({
    super.key,
    this.controller,
    this.hintText = 'Search...',
    this.onChanged,
    this.onSubmitted,
    this.onClear,
    this.enabled = true,
    this.autofocus = false,
    this.focusNode,
  });

  final TextEditingController? controller;
  final String hintText;
  final ValueChanged<String>? onChanged;
  final ValueChanged<String>? onSubmitted;
  final VoidCallback? onClear;
  final bool enabled;
  final bool autofocus;
  final FocusNode? focusNode;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.surfaceColor,
        borderRadius: AppBorderRadius.textField,
        border: Border.all(
          color: AppColors.borderColor,
          width: AppComponents.textFieldBorderWidth,
        ),
      ),
      child: TextField(
        controller: controller,
        enabled: enabled,
        autofocus: autofocus,
        focusNode: focusNode,
        onChanged: onChanged,
        onSubmitted: onSubmitted,
        style: AppTypography.bodyMedium,
        decoration: InputDecoration(
          hintText: hintText,
          hintStyle: AppTypography.hint,
          prefixIcon: const Icon(
            Icons.search,
            color: AppColors.onSurfaceColor,
            size: AppComponents.iconSmall,
          ),
          suffixIcon: controller != null && controller!.text.isNotEmpty
              ? IconButton(
                  icon: const Icon(
                    Icons.clear,
                    color: AppColors.onSurfaceColor,
                    size: AppComponents.iconSmall,
                  ),
                  onPressed: () {
                    controller!.clear();
                    onClear?.call();
                    onChanged?.call('');
                  },
                )
              : null,
          border: InputBorder.none,
          enabledBorder: InputBorder.none,
          focusedBorder: InputBorder.none,
          errorBorder: InputBorder.none,
          focusedErrorBorder: InputBorder.none,
          disabledBorder: InputBorder.none,
          contentPadding: AppSpacing.textFieldPadding,
        ),
      ),
    );
  }
}

/// Bottom sheet text field container based on existing BottomSheetTextFiledContainer
class AppBottomSheetTextField extends StatelessWidget {
  const AppBottomSheetTextField({
    super.key,
    this.controller,
    this.labelText,
    this.hintText,
    this.errorText,
    this.prefixIcon,
    this.suffixIcon,
    this.obscureText = false,
    this.enabled = true,
    this.maxLines = 1,
    this.keyboardType,
    this.textInputAction,
    this.onChanged,
    this.onSubmitted,
    this.validator,
    this.inputFormatters,
    this.focusNode,
    this.autofocus = false,
  });

  final TextEditingController? controller;
  final String? labelText;
  final String? hintText;
  final String? errorText;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final bool obscureText;
  final bool enabled;
  final int maxLines;
  final TextInputType? keyboardType;
  final TextInputAction? textInputAction;
  final ValueChanged<String>? onChanged;
  final ValueChanged<String>? onSubmitted;
  final String? Function(String?)? validator;
  final List<TextInputFormatter>? inputFormatters;
  final FocusNode? focusNode;
  final bool autofocus;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: AppSpacing.xSmall),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (labelText != null) ...[
            Padding(
              padding: const EdgeInsets.only(
                left: AppSpacing.small,
                bottom: AppSpacing.xSmall,
              ),
              child: Text(
                labelText!,
                style: AppTypography.labelMedium,
              ),
            ),
          ],
          Container(
            decoration: BoxDecoration(
              color: AppColors.pageBackgroundColor,
              borderRadius: AppBorderRadius.textField,
              border: Border.all(
                color: errorText != null ? AppColors.errorColor : AppColors.borderColor,
                width: AppComponents.textFieldBorderWidth,
              ),
            ),
            child: TextFormField(
              controller: controller,
              obscureText: obscureText,
              enabled: enabled,
              maxLines: maxLines,
              keyboardType: keyboardType,
              textInputAction: textInputAction,
              onChanged: onChanged,
              onFieldSubmitted: onSubmitted,
              validator: validator,
              inputFormatters: inputFormatters,
              focusNode: focusNode,
              autofocus: autofocus,
              style: AppTypography.bodyMedium,
              decoration: InputDecoration(
                hintText: hintText,
                hintStyle: AppTypography.hint,
                prefixIcon: prefixIcon,
                suffixIcon: suffixIcon,
                border: InputBorder.none,
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                errorBorder: InputBorder.none,
                focusedErrorBorder: InputBorder.none,
                disabledBorder: InputBorder.none,
                contentPadding: AppSpacing.textFieldPadding,
              ),
            ),
          ),
          if (errorText != null) ...[
            Padding(
              padding: const EdgeInsets.only(
                left: AppSpacing.small,
                top: AppSpacing.xxSmall,
              ),
              child: Text(
                errorText!,
                style: AppTypography.error,
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// Verification code input field for OTP/PIN entry
class AppVerificationCodeField extends StatelessWidget {
  const AppVerificationCodeField({
    super.key,
    this.controller,
    this.length = 6,
    this.onChanged,
    this.onCompleted,
    this.enabled = true,
    this.autofocus = false,
    this.focusNode,
  });

  final TextEditingController? controller;
  final int length;
  final ValueChanged<String>? onChanged;
  final ValueChanged<String>? onCompleted;
  final bool enabled;
  final bool autofocus;
  final FocusNode? focusNode;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: AppSpacing.medium),
      child: TextField(
        controller: controller,
        enabled: enabled,
        autofocus: autofocus,
        focusNode: focusNode,
        keyboardType: TextInputType.number,
        textAlign: TextAlign.center,
        maxLength: length,
        onChanged: (value) {
          onChanged?.call(value);
          if (value.length == length) {
            onCompleted?.call(value);
          }
        },
        inputFormatters: [
          FilteringTextInputFormatter.digitsOnly,
          LengthLimitingTextInputFormatter(length),
        ],
        style: AppTypography.heading2.copyWith(
          letterSpacing: AppSpacing.medium,
        ),
        decoration: InputDecoration(
          counterText: '',
          hintText: '●' * length,
          hintStyle: AppTypography.heading2.copyWith(
            color: AppColors.hintTextColor,
            letterSpacing: AppSpacing.medium,
          ),
          filled: true,
          fillColor: AppColors.surfaceColor,
          border: OutlineInputBorder(
            borderRadius: AppBorderRadius.textField,
            borderSide: const BorderSide(
              color: AppColors.borderColor,
              width: AppComponents.textFieldBorderWidth,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: AppBorderRadius.textField,
            borderSide: const BorderSide(
              color: AppColors.borderColor,
              width: AppComponents.textFieldBorderWidth,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: AppBorderRadius.textField,
            borderSide: const BorderSide(
              color: AppColors.primaryColor,
              width: 2.0,
            ),
          ),
          contentPadding: const EdgeInsets.symmetric(
            vertical: AppSpacing.large,
          ),
        ),
      ),
    );
  }
}

/// Dropdown text field container based on existing dropdown patterns
class AppDropdownTextField<T> extends StatelessWidget {
  const AppDropdownTextField({
    super.key,
    required this.items,
    required this.onChanged,
    this.value,
    this.labelText,
    this.hintText,
    this.errorText,
    this.prefixIcon,
    this.enabled = true,
    this.validator,
  });

  final List<DropdownMenuItem<T>> items;
  final ValueChanged<T?>? onChanged;
  final T? value;
  final String? labelText;
  final String? hintText;
  final String? errorText;
  final Widget? prefixIcon;
  final bool enabled;
  final String? Function(T?)? validator;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (labelText != null) ...[
          Text(
            labelText!,
            style: AppTypography.labelMedium,
          ),
          const SizedBox(height: AppSpacing.xSmall),
        ],
        Container(
          decoration: BoxDecoration(
            color: AppColors.surfaceColor,
            borderRadius: AppBorderRadius.textField,
            border: Border.all(
              color: errorText != null ? AppColors.errorColor : AppColors.borderColor,
              width: AppComponents.textFieldBorderWidth,
            ),
          ),
          child: DropdownButtonFormField<T>(
            value: value,
            items: items,
            onChanged: enabled ? onChanged : null,
            validator: validator,
            style: AppTypography.bodyMedium,
            decoration: InputDecoration(
              hintText: hintText,
              hintStyle: AppTypography.hint,
              prefixIcon: prefixIcon,
              border: InputBorder.none,
              enabledBorder: InputBorder.none,
              focusedBorder: InputBorder.none,
              errorBorder: InputBorder.none,
              focusedErrorBorder: InputBorder.none,
              disabledBorder: InputBorder.none,
              contentPadding: AppSpacing.textFieldPadding,
            ),
            icon: const Icon(
              Icons.keyboard_arrow_down,
              color: AppColors.onSurfaceColor,
            ),
            isExpanded: true,
          ),
        ),
        if (errorText != null) ...[
          Padding(
            padding: const EdgeInsets.only(
              left: AppSpacing.small,
              top: AppSpacing.xxSmall,
            ),
            child: Text(
              errorText!,
              style: AppTypography.error,
            ),
          ),
        ],
      ],
    );
  }
}
