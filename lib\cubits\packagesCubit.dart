import 'package:eschool/data/models/package.dart';
import 'package:eschool/data/repositories/packageRepository.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

abstract class PackagesState {}

class PackagesInitial extends PackagesState {}

class PackagesLoading extends PackagesState {}

class PackagesFetchSuccess extends PackagesState {
  final List<Package> packages;

  PackagesFetchSuccess({required this.packages});
}

class PackagesFetchFailure extends PackagesState {
  final String errorMessage;

  PackagesFetchFailure({required this.errorMessage});
}

class PackagesCubit extends Cubit<PackagesState> {
  final PackageRepository _packageRepository;
  final bool useDummyData;

  PackagesCubit({
    required PackageRepository packageRepository,
    this.useDummyData = false,
  })  : _packageRepository = packageRepository,
        super(PackagesInitial());

  Future<void> fetchPackages({int? subjectId, int? educationStageId}) async {
    try {
      emit(PackagesLoading());

      List<Package> packages;

      if (useDummyData) {
        packages = _packageRepository.getDummyPackages(
          subjectId: subjectId, 
          educationStageId: educationStageId
        );
      } else {
        packages = await _packageRepository.fetchPackages(
          subjectId: subjectId, 
          educationStageId: educationStageId
        );
      }

      emit(PackagesFetchSuccess(packages: packages));
    } catch (e) {
      emit(PackagesFetchFailure(errorMessage: e.toString()));
    }
  }
}


