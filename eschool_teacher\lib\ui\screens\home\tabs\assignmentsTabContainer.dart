import 'package:flutter/material.dart';
import 'package:eschool_teacher/ui/design_system/design_system.dart';
import 'package:eschool_teacher/ui/screens/home/<USER>/assignments_grid_widget.dart';
import 'package:eschool_teacher/app/routes.dart';

/// Assignments tab container for assignment management
/// Matches the design from the provided screenshot
class AssignmentsTabContainer extends StatelessWidget {
  const AssignmentsTabContainer({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppColors.pageBackgroundColor,
      child: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppSpacing.medium),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'الواجبات النشطة',
                    style: AppTypography.heading2,
                  ),
                  AppIconButton(
                    onPressed: () {
                      Navigator.of(context).pushNamed(
                        Routes.addAssignment,
                        arguments: {
                          'editAssignment': false,
                          'assignment': null,
                        },
                      );
                    },
                    icon: Icons.add,
                    backgroundColor: AppColors.primaryColor,
                    iconColor: AppColors.onPrimaryColor,
                  ),
                ],
              ),
              
              const SizedBox(height: AppSpacing.large),
              
              // Assignments Grid (Full View)
              _buildAssignmentsGrid(context),
              
              const SizedBox(height: AppSpacing.large),
              
              // Student Submissions Section
              Text(
                'تسليمات الطلاب',
                style: AppTypography.heading4,
              ),
              
              const SizedBox(height: AppSpacing.medium),
              
              // Student Submissions List
              _buildStudentSubmissions(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAssignmentsGrid(BuildContext context) {
    // Dummy assignment data
    final assignments = [
      {
        'id': '1',
        'title': 'الوحدة الأولى',
        'subject': 'اللغة العربية',
        'icon': Icons.book,
        'color': AppColors.primaryColor,
        'submissionsCount': 5,
        'dueDate': 'غداً',
      },
      {
        'id': '2',
        'title': 'التمارين الرياضية',
        'subject': 'الرياضيات',
        'icon': Icons.calculate,
        'color': AppColors.successColor,
        'submissionsCount': 3,
        'dueDate': 'بعد يومين',
      },
      {
        'id': '3',
        'title': 'قوانين نيوتن',
        'subject': 'الفيزياء',
        'icon': Icons.science,
        'color': AppColors.infoColor,
        'submissionsCount': 7,
        'dueDate': 'الأسبوع القادم',
      },
      {
        'id': '4',
        'title': 'النحو والصرف',
        'subject': 'اللغة العربية',
        'icon': Icons.text_fields,
        'color': AppColors.warningColor,
        'submissionsCount': 2,
        'dueDate': 'الشهر القادم',
      },
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1.2,
        crossAxisSpacing: AppSpacing.medium,
        mainAxisSpacing: AppSpacing.medium,
      ),
      itemCount: assignments.length + 1, // +1 for add button
      itemBuilder: (context, index) {
        if (index == 0) {
          return _buildAddAssignmentCard(context);
        }
        
        final assignment = assignments[index - 1];
        return _buildAssignmentCard(context, assignment);
      },
    );
  }

  Widget _buildAddAssignmentCard(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Navigator.of(context).pushNamed(
          Routes.addAssignment,
          arguments: {
            'editAssignment': false,
            'assignment': null,
          },
        );
      },
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.surfaceColor,
          borderRadius: AppBorderRadius.assignmentCard,
          border: Border.all(
            color: AppColors.primaryColor,
            width: 2,
            style: BorderStyle.solid,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(AppSpacing.medium),
              decoration: BoxDecoration(
                color: AppColors.primaryColor,
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.add,
                size: AppComponents.iconLarge,
                color: AppColors.onPrimaryColor,
              ),
            ),
            const SizedBox(height: AppSpacing.small),
            Text(
              'إضافة واجب',
              style: AppTypography.labelLarge.copyWith(
                color: AppColors.primaryColor,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAssignmentCard(BuildContext context, Map<String, dynamic> assignment) {
    return GestureDetector(
      onTap: () {
        Navigator.of(context).pushNamed(
          Routes.assignment,
          arguments: assignment,
        );
      },
      child: AppCard(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Assignment Icon and Title
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(AppSpacing.xSmall),
                  decoration: BoxDecoration(
                    color: (assignment['color'] as Color).withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    assignment['icon'] as IconData,
                    size: AppComponents.iconMedium,
                    color: assignment['color'] as Color,
                  ),
                ),
                const SizedBox(width: AppSpacing.small),
                Expanded(
                  child: Text(
                    assignment['title'] as String,
                    style: AppTypography.labelLarge.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppSpacing.small),
            
            // Subject
            Text(
              assignment['subject'] as String,
              style: AppTypography.bodySmall.copyWith(
                color: AppColors.onSurfaceColor,
              ),
            ),
            
            const SizedBox(height: AppSpacing.small),
            
            // Due Date and Submissions
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  assignment['dueDate'] as String,
                  style: AppTypography.caption.copyWith(
                    color: AppColors.onSurfaceColor,
                  ),
                ),
                if (assignment['submissionsCount'] > 0)
                  AppStatusBadge(
                    text: '${assignment['submissionsCount']}',
                    color: AppColors.warningColor,
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStudentSubmissions(BuildContext context) {
    // Dummy submission data
    final submissions = [
      {
        'studentName': 'سعود عبد الرحمن',
        'assignmentTitle': 'واجب الرياضيات 1',
        'submissionDate': 'منذ ساعتين',
        'status': 'pending',
      },
      {
        'studentName': 'فاطمة أحمد',
        'assignmentTitle': 'واجب اللغة العربية',
        'submissionDate': 'أمس',
        'status': 'reviewed',
      },
      {
        'studentName': 'محمد علي',
        'assignmentTitle': 'واجب الفيزياء',
        'submissionDate': 'منذ 3 أيام',
        'status': 'pending',
      },
    ];

    return Column(
      children: submissions.map((submission) {
        return Container(
          width: double.infinity,
          margin: const EdgeInsets.only(bottom: AppSpacing.small),
          child: AppCard(
            child: Row(
              children: [
                // Student Avatar
                AppAvatar(
                  name: submission['studentName'] as String,
                  size: 40,
                ),
                
                const SizedBox(width: AppSpacing.medium),
                
                // Submission Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        submission['assignmentTitle'] as String,
                        style: AppTypography.bodyMedium.copyWith(
                          fontWeight: FontWeight.medium,
                        ),
                      ),
                      const SizedBox(height: AppSpacing.xxSmall),
                      Text(
                        submission['studentName'] as String,
                        style: AppTypography.bodySmall.copyWith(
                          color: AppColors.onSurfaceColor,
                        ),
                      ),
                      const SizedBox(height: AppSpacing.xxSmall),
                      Text(
                        submission['submissionDate'] as String,
                        style: AppTypography.caption.copyWith(
                          color: AppColors.onSurfaceColor,
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Status and Action
                Column(
                  children: [
                    AppStatusBadge(
                      text: submission['status'] == 'pending' ? 'جديد' : 'تم المراجعة',
                      color: submission['status'] == 'pending' 
                          ? AppColors.warningColor 
                          : AppColors.successColor,
                    ),
                    const SizedBox(height: AppSpacing.xSmall),
                    AppIconButton(
                      onPressed: () {
                        // Navigate to submission details
                      },
                      icon: Icons.arrow_forward_ios,
                      size: AppComponents.iconXSmall,
                      iconColor: AppColors.onSurfaceColor,
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }
}
