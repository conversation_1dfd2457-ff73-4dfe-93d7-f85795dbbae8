import 'dart:convert';
import 'package:eschool/data/models/package.dart';
import 'package:eschool/data/models/subject.dart';
import 'package:eschool/utils/api.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

class PackageRepository {
  // Cache key for packages
  static const String _cacheKey = 'packages_cache';
  // Cache expiration time (in milliseconds) - default 3 hours
  static const int _cacheExpirationTime = 10800000; // 3 hours

  Future<List<Package>> fetchPackages({int? subjectId, int? educationStageId}) async {
    try {
      // Check if we have valid cached data first
      final cachedData = await _getCachedPackages();
      if (cachedData != null) {
        debugPrint('PackageRepository: Using cached packages');
        // Apply filters to cached data
        return _filterPackages(cachedData, subjectId, educationStageId);
      }

      // No valid cache, fetch from API
      debugPrint('PackageRepository: Fetching packages from API');
      final Map<String, dynamic> queryParameters = {};

      if (subjectId != null) {
        queryParameters['subject_id'] = subjectId;
      }

      if (educationStageId != null) {
        queryParameters['education_stage_id'] = educationStageId;
      }

      final result = await Api.get(
        url: Api.packages,
        useAuthToken: true,
        queryParameters: queryParameters.isNotEmpty ? queryParameters : null,
      );

      debugPrint('PackageRepository: API response: $result');

      // Handle the actual API response format from Postman
      if (result['packages'] != null) {
        final List<dynamic> packagesData = result['packages'] as List;
        final List<Package> packages = packagesData
            .map((package) => Package.fromJson(Map<String, dynamic>.from(package)))
            .toList();

        debugPrint('PackageRepository: Successfully parsed ${packages.length} packages from API');

        // Cache the results
        await _cachePackages(packages);

        // Return filtered packages
        return _filterPackages(packages, subjectId, educationStageId);
      } else {
        debugPrint('PackageRepository: No packages found in API response, falling back to dummy data');
      }

      // Fallback to dummy data if API fails
      return getDummyPackages(subjectId: subjectId, educationStageId: educationStageId);
    } catch (e) {
      debugPrint('PackageRepository: Error fetching packages: $e');
      // On error, try to use cached data if available, or fallback to dummy data
      final cachedData = await _getCachedPackages();
      if (cachedData != null) {
        debugPrint('PackageRepository: Using cached packages after error');
        return _filterPackages(cachedData, subjectId, educationStageId);
      }
      return getDummyPackages(subjectId: subjectId, educationStageId: educationStageId);
    }
  }

  // Filter packages based on subject and level
  List<Package> _filterPackages(List<Package> packages, int? subjectId, int? educationStageId) {
    if (subjectId == null && educationStageId == null) {
      return packages;
    }

    return packages.where((package) {
      bool matchesSubject = subjectId == null || package.subject?.id == subjectId;
      bool matchesEducationStage = educationStageId == null ||
                                  package.educationStage?.id == educationStageId;
      return matchesSubject && matchesEducationStage;
    }).toList();
  }

  // Cache packages data
  Future<void> _cachePackages(List<Package> packages) async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      final Map<String, dynamic> cacheData = {
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'packages': packages.map((package) => package.toJson()).toList(),
      };

      await prefs.setString(_cacheKey, jsonEncode(cacheData));
      debugPrint('PackageRepository: Packages cached successfully');
    } catch (e) {
      debugPrint('PackageRepository: Error caching packages: $e');
    }
  }

  // Get cached packages data if it's still valid
  Future<List<Package>?> _getCachedPackages() async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      final String? cachedDataString = prefs.getString(_cacheKey);

      if (cachedDataString == null || cachedDataString.isEmpty) {
        return null;
      }

      final Map<String, dynamic> cachedData = jsonDecode(cachedDataString);
      final int timestamp = cachedData['timestamp'] ?? 0;
      final int currentTime = DateTime.now().millisecondsSinceEpoch;

      // Check if cache is still valid
      if (currentTime - timestamp > _cacheExpirationTime) {
        debugPrint('PackageRepository: Cache expired');
        return null;
      }

      // Parse cached packages
      final List<dynamic> packagesList = cachedData['packages'] ?? [];
      final List<Package> packages = packagesList
          .map((packageJson) => Package.fromJson(Map<String, dynamic>.from(packageJson)))
          .toList();

      return packages;
    } catch (e) {
      debugPrint('PackageRepository: Error reading cache: $e');
      return null;
    }
  }

  // For testing with dummy data
  List<Package> getDummyPackages({int? subjectId, int? educationStageId}) {
    final List<Package> allPackages = [
      Package(
        id: 1,
        name: "Arabic",
        description: "Arabic",
        price: 100.0,
        type: "package",
        packageScope: "individual",
        lectureCredits: 5,
        subject: Subject(id: 1, title: "Arabic", imageUrl: ""),
        educationStage: EducationStage(id: 1, name: "Primary"),
      ),
      Package(
        id: 2,
        name: "Arabic",
        description: "Arabic",
        price: 50.0,
        type: "package",
        packageScope: "group",
        lectureCredits: 3,
        subject: Subject(id: 1, title: "Arabic", imageUrl: ""),
        educationStage: EducationStage(id: 2, name: "Middle"),
      ),
      Package(
        id: 3,
        name: "English",
        description: "English",
        price: 75.0,
        type: "package",
        packageScope: "individual",
        lectureCredits: 5,
        subject: Subject(id: 2, title: "English", imageUrl: ""),
        educationStage: EducationStage(id: 2, name: "Middle"),
      ),
      Package(
        id: 4,
        name: "History",
        description: "History",
        price: 50.0,
        type: "package",
        packageScope: "group",
        lectureCredits: 5,
        subject: Subject(id: 3, title: "History", imageUrl: ""),
        educationStage: null,
      ),
    ];

    return _filterPackages(allPackages, subjectId, educationStageId);
  }
}