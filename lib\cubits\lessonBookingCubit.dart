import 'package:bloc/bloc.dart';
import 'package:eschool/data/models/classLesson.dart';
import 'package:eschool/data/models/student.dart';
import 'package:equatable/equatable.dart';

part 'lessonBookingState.dart';

class LessonBookingCubit extends Cubit<LessonBookingState> {
  LessonBookingCubit() : super(LessonBookingInitial());

  // Initialize the booking session with subject, level, and student details
  void initBookingSession({
    required String subject,
    required String? level,
    required Student student,
  }) {
    final int availableCredits = student.getPointsFor(subject, level ?? "");
    final bool hasCredits = availableCredits > 0;

    emit(LessonBookingOptions(
      subject: subject,
      level: level,
      availableCredits: availableCredits,
      hasCredits: hasCredits,
      selectedLessons: [],
      maxSelections: availableCredits,
    ));
  }

  // Use existing credits for booking
  void useExistingCredits() {
    if (state is LessonBookingOptions) {
      final currentState = state as LessonBookingOptions;
      emit(LessonSelectionInProgress(
        subject: currentState.subject,
        level: currentState.level,
        availableCredits: currentState.availableCredits,
        selectedLessons: [],
        maxSelections: currentState.availableCredits,
      ));
    }
  }

  // Purchase a new package
  void purchaseNewPackage() {
    if (state is LessonBookingOptions) {
      final currentState = state as LessonBookingOptions;
      emit(PackageSelectionNeeded(
        subject: currentState.subject,
        level: currentState.level,
      ));
    }
  }

  // Select lesson
  void selectLesson(ClassLesson lesson) {
    if (state is LessonSelectionInProgress) {
      final currentState = state as LessonSelectionInProgress;

      // Check if we've reached the maximum number of selections
      if (currentState.selectedLessons.length >= currentState.maxSelections) {
        // Can't select more lessons
        return;
      }

      // Check if the lesson is already selected
      if (currentState.selectedLessons.contains(lesson)) {
        // Already selected, do nothing
        return;
      }

      // Add the lesson to the selected lessons
      List<ClassLesson> updatedLessons = List.from(currentState.selectedLessons)..add(lesson);

      emit(LessonSelectionInProgress(
        subject: currentState.subject,
        level: currentState.level,
        availableCredits: currentState.availableCredits,
        selectedLessons: updatedLessons,
        maxSelections: currentState.maxSelections,
      ));
    }
  }

  // Deselect lesson
  void deselectLesson(ClassLesson lesson) {
    if (state is LessonSelectionInProgress) {
      final currentState = state as LessonSelectionInProgress;

      // Remove the lesson from the selected lessons
      List<ClassLesson> updatedLessons = List.from(currentState.selectedLessons)
        ..removeWhere((l) => l.id == lesson.id);

      emit(LessonSelectionInProgress(
        subject: currentState.subject,
        level: currentState.level,
        availableCredits: currentState.availableCredits,
        selectedLessons: updatedLessons,
        maxSelections: currentState.maxSelections,
      ));
    }
  }

  // Confirm bookings
  void confirmBookings() {
    if (state is LessonSelectionInProgress) {
      final currentState = state as LessonSelectionInProgress;

      // In a real app, this would make an API call to confirm the bookings

      emit(BookingConfirmed(
        subject: currentState.subject,
        level: currentState.level,
        bookedLessons: currentState.selectedLessons,
      ));
    }
  }

  // Reset to initial state
  void reset() {
    emit(LessonBookingInitial());
  }
}