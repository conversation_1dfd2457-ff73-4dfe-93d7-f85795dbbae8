enum WithdrawalStatus { pending, processing, completed, rejected, cancelled }

class WithdrawalRequest {
  final String id;
  final String teacherId;
  final double amount;
  final String currency;
  final String iban;
  final String bankName;
  final String accountHolderName;
  final WithdrawalStatus status;
  final String? notes;
  final String? rejectionReason;
  final DateTime requestedAt;
  final DateTime? processedAt;
  final String? transactionReference;

  WithdrawalRequest({
    required this.id,
    required this.teacherId,
    required this.amount,
    this.currency = 'QAR',
    required this.iban,
    required this.bankName,
    required this.accountHolderName,
    required this.status,
    this.notes,
    this.rejectionReason,
    required this.requestedAt,
    this.processedAt,
    this.transactionReference,
  });

  // Helper getters
  bool get isPending => status == WithdrawalStatus.pending;
  bool get isProcessing => status == WithdrawalStatus.processing;
  bool get isCompleted => status == WithdrawalStatus.completed;
  bool get isRejected => status == WithdrawalStatus.rejected;
  bool get isCancelled => status == WithdrawalStatus.cancelled;
  bool get canBeCancelled => isPending;

  String get formattedAmount => '${amount.toStringAsFixed(2)} $currency';
  
  String get maskedIban {
    if (iban.length <= 8) return iban;
    final start = iban.substring(0, 4);
    final end = iban.substring(iban.length - 4);
    final middle = '*' * (iban.length - 8);
    return '$start$middle$end';
  }

  String get formattedRequestDate {
    return '${requestedAt.day}/${requestedAt.month}/${requestedAt.year}';
  }

  String get formattedProcessedDate {
    if (processedAt == null) return '-';
    return '${processedAt!.day}/${processedAt!.month}/${processedAt!.year}';
  }

  String get statusDisplayName {
    switch (status) {
      case WithdrawalStatus.pending:
        return 'قيد الانتظار';
      case WithdrawalStatus.processing:
        return 'قيد المعالجة';
      case WithdrawalStatus.completed:
        return 'مكتمل';
      case WithdrawalStatus.rejected:
        return 'مرفوض';
      case WithdrawalStatus.cancelled:
        return 'ملغي';
    }
  }

  String get statusDescription {
    switch (status) {
      case WithdrawalStatus.pending:
        return 'طلب السحب قيد المراجعة';
      case WithdrawalStatus.processing:
        return 'جاري معالجة طلب السحب';
      case WithdrawalStatus.completed:
        return 'تم تحويل المبلغ بنجاح';
      case WithdrawalStatus.rejected:
        return rejectionReason ?? 'تم رفض طلب السحب';
      case WithdrawalStatus.cancelled:
        return 'تم إلغاء طلب السحب';
    }
  }

  // Copy constructor for immutability
  WithdrawalRequest copyWith({
    String? id,
    String? teacherId,
    double? amount,
    String? currency,
    String? iban,
    String? bankName,
    String? accountHolderName,
    WithdrawalStatus? status,
    String? notes,
    String? rejectionReason,
    DateTime? requestedAt,
    DateTime? processedAt,
    String? transactionReference,
  }) {
    return WithdrawalRequest(
      id: id ?? this.id,
      teacherId: teacherId ?? this.teacherId,
      amount: amount ?? this.amount,
      currency: currency ?? this.currency,
      iban: iban ?? this.iban,
      bankName: bankName ?? this.bankName,
      accountHolderName: accountHolderName ?? this.accountHolderName,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      rejectionReason: rejectionReason ?? this.rejectionReason,
      requestedAt: requestedAt ?? this.requestedAt,
      processedAt: processedAt ?? this.processedAt,
      transactionReference: transactionReference ?? this.transactionReference,
    );
  }

  factory WithdrawalRequest.fromJson(Map<String, dynamic> json) {
    return WithdrawalRequest(
      id: json['id'] ?? '',
      teacherId: json['teacher_id'] ?? '',
      amount: (json['amount'] ?? 0.0).toDouble(),
      currency: json['currency'] ?? 'QAR',
      iban: json['iban'] ?? '',
      bankName: json['bank_name'] ?? '',
      accountHolderName: json['account_holder_name'] ?? '',
      status: WithdrawalStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => WithdrawalStatus.pending,
      ),
      notes: json['notes'],
      rejectionReason: json['rejection_reason'],
      requestedAt: DateTime.parse(json['requested_at']),
      processedAt: json['processed_at'] != null 
          ? DateTime.parse(json['processed_at'])
          : null,
      transactionReference: json['transaction_reference'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'teacher_id': teacherId,
      'amount': amount,
      'currency': currency,
      'iban': iban,
      'bank_name': bankName,
      'account_holder_name': accountHolderName,
      'status': status.toString().split('.').last,
      'notes': notes,
      'rejection_reason': rejectionReason,
      'requested_at': requestedAt.toIso8601String(),
      'processed_at': processedAt?.toIso8601String(),
      'transaction_reference': transactionReference,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is WithdrawalRequest && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'WithdrawalRequest(id: $id, amount: $amount, status: $status, requestedAt: $requestedAt)';
  }
}
