import 'package:eschool_teacher/data/models/withdrawal_request.dart';

class WithdrawalRequestRepository {
  // Generate dummy withdrawal requests
  static List<WithdrawalRequest> _generateDummyRequests() {
    final now = DateTime.now();
    final requests = <WithdrawalRequest>[];

    // Recent completed withdrawal
    requests.add(WithdrawalRequest(
      id: 'wd_1',
      teacherId: 'teacher_1',
      amount: 500.0,
      iban: 'QA58DOHB0000*********0ABCDEFG',
      bankName: 'بنك قطر الوطني',
      accountHolderName: 'سعيد أحمد المنصوري',
      status: WithdrawalStatus.completed,
      notes: 'سحب الأرباح الشهرية',
      requestedAt: now.subtract(const Duration(days: 7)),
      processedAt: now.subtract(const Duration(days: 6)),
      transactionReference: 'TXN*********',
    ));

    // Pending withdrawal
    requests.add(WithdrawalRequest(
      id: 'wd_2',
      teacherId: 'teacher_1',
      amount: 300.0,
      iban: 'QA58DOHB0000*********0ABCDEFG',
      bankName: 'بنك قطر الوطني',
      accountHolderName: 'سعيد أحمد المنصوري',
      status: WithdrawalStatus.pending,
      notes: 'سحب جزئي للأرباح',
      requestedAt: now.subtract(const Duration(days: 2)),
    ));

    // Processing withdrawal
    requests.add(WithdrawalRequest(
      id: 'wd_3',
      teacherId: 'teacher_1',
      amount: 200.0,
      iban: 'QA58DOHB0000*********0ABCDEFG',
      bankName: 'بنك قطر الوطني',
      accountHolderName: 'سعيد أحمد المنصوري',
      status: WithdrawalStatus.processing,
      notes: 'سحب عاجل',
      requestedAt: now.subtract(const Duration(days: 1)),
    ));

    // Rejected withdrawal (example)
    requests.add(WithdrawalRequest(
      id: 'wd_4',
      teacherId: 'teacher_1',
      amount: 1000.0,
      iban: 'QA58DOHB0000*********0ABCDEFG',
      bankName: 'بنك قطر الوطني',
      accountHolderName: 'سعيد أحمد المنصوري',
      status: WithdrawalStatus.rejected,
      notes: 'سحب كبير',
      rejectionReason: 'المبلغ المطلوب يتجاوز الرصيد المتاح',
      requestedAt: now.subtract(const Duration(days: 10)),
      processedAt: now.subtract(const Duration(days: 9)),
    ));

    // Cancelled withdrawal
    requests.add(WithdrawalRequest(
      id: 'wd_5',
      teacherId: 'teacher_1',
      amount: 150.0,
      iban: 'QA58DOHB0000*********0ABCDEFG',
      bankName: 'بنك قطر الوطني',
      accountHolderName: 'سعيد أحمد المنصوري',
      status: WithdrawalStatus.cancelled,
      notes: 'سحب تجريبي',
      requestedAt: now.subtract(const Duration(days: 15)),
      processedAt: now.subtract(const Duration(days: 14)),
    ));

    // More historical requests
    for (int i = 6; i <= 10; i++) {
      requests.add(WithdrawalRequest(
        id: 'wd_$i',
        teacherId: 'teacher_1',
        amount: 250.0 + (i * 50.0),
        iban: 'QA58DOHB0000*********0ABCDEFG',
        bankName: 'بنك قطر الوطني',
        accountHolderName: 'سعيد أحمد المنصوري',
        status: WithdrawalStatus.completed,
        notes: 'سحب شهري رقم $i',
        requestedAt: now.subtract(Duration(days: 15 + (i * 7))),
        processedAt: now.subtract(Duration(days: 14 + (i * 7))),
        transactionReference: 'TXN${********* + i}',
      ));
    }

    return requests;
  }

  static List<WithdrawalRequest> _dummyRequests = _generateDummyRequests();

  // Get all withdrawal requests
  Future<List<WithdrawalRequest>> getAllRequests() async {
    await Future.delayed(const Duration(milliseconds: 500)); // Simulate network delay
    return List.from(_dummyRequests)..sort((a, b) => b.requestedAt.compareTo(a.requestedAt));
  }

  // Get requests by status
  Future<List<WithdrawalRequest>> getRequestsByStatus(WithdrawalStatus status) async {
    await Future.delayed(const Duration(milliseconds: 300));
    return _dummyRequests.where((r) => r.status == status).toList()
      ..sort((a, b) => b.requestedAt.compareTo(a.requestedAt));
  }

  // Get pending requests
  Future<List<WithdrawalRequest>> getPendingRequests() async {
    await Future.delayed(const Duration(milliseconds: 300));
    return _dummyRequests.where((r) => r.isPending).toList()
      ..sort((a, b) => b.requestedAt.compareTo(a.requestedAt));
  }

  // Get completed requests
  Future<List<WithdrawalRequest>> getCompletedRequests() async {
    await Future.delayed(const Duration(milliseconds: 300));
    return _dummyRequests.where((r) => r.isCompleted).toList()
      ..sort((a, b) => b.requestedAt.compareTo(a.requestedAt));
  }

  // Get request by ID
  Future<WithdrawalRequest?> getRequestById(String id) async {
    await Future.delayed(const Duration(milliseconds: 200));
    try {
      return _dummyRequests.firstWhere((r) => r.id == id);
    } catch (e) {
      return null;
    }
  }

  // Create new withdrawal request
  Future<WithdrawalRequest> createRequest({
    required double amount,
    required String iban,
    required String bankName,
    required String accountHolderName,
    String? notes,
  }) async {
    await Future.delayed(const Duration(milliseconds: 800)); // Simulate network delay
    
    final request = WithdrawalRequest(
      id: 'wd_${DateTime.now().millisecondsSinceEpoch}',
      teacherId: 'teacher_1',
      amount: amount,
      iban: iban,
      bankName: bankName,
      accountHolderName: accountHolderName,
      status: WithdrawalStatus.pending,
      notes: notes,
      requestedAt: DateTime.now(),
    );
    
    _dummyRequests.add(request);
    return request;
  }

  // Cancel withdrawal request
  Future<WithdrawalRequest> cancelRequest(String requestId) async {
    await Future.delayed(const Duration(milliseconds: 600));
    
    final index = _dummyRequests.indexWhere((r) => r.id == requestId);
    if (index != -1) {
      final request = _dummyRequests[index];
      if (request.canBeCancelled) {
        _dummyRequests[index] = request.copyWith(
          status: WithdrawalStatus.cancelled,
          processedAt: DateTime.now(),
        );
        return _dummyRequests[index];
      } else {
        throw Exception('Cannot cancel request in current status');
      }
    }
    
    throw Exception('Request not found');
  }

  // Update request status (admin function - for simulation)
  Future<WithdrawalRequest> updateRequestStatus({
    required String requestId,
    required WithdrawalStatus status,
    String? rejectionReason,
    String? transactionReference,
  }) async {
    await Future.delayed(const Duration(milliseconds: 700));
    
    final index = _dummyRequests.indexWhere((r) => r.id == requestId);
    if (index != -1) {
      _dummyRequests[index] = _dummyRequests[index].copyWith(
        status: status,
        rejectionReason: rejectionReason,
        transactionReference: transactionReference,
        processedAt: DateTime.now(),
      );
      return _dummyRequests[index];
    }
    
    throw Exception('Request not found');
  }

  // Get withdrawal statistics
  Future<Map<String, dynamic>> getWithdrawalStats() async {
    await Future.delayed(const Duration(milliseconds: 400));
    
    final completed = _dummyRequests.where((r) => r.isCompleted).toList();
    final pending = _dummyRequests.where((r) => r.isPending).toList();
    final rejected = _dummyRequests.where((r) => r.isRejected).toList();
    
    final totalWithdrawn = completed.fold(0.0, (sum, r) => sum + r.amount);
    final pendingAmount = pending.fold(0.0, (sum, r) => sum + r.amount);
    
    // This month's withdrawals
    final now = DateTime.now();
    final thisMonth = completed.where((r) => 
        r.processedAt != null &&
        r.processedAt!.year == now.year && 
        r.processedAt!.month == now.month).toList();
    final thisMonthAmount = thisMonth.fold(0.0, (sum, r) => sum + r.amount);
    
    return {
      'total_requests': _dummyRequests.length,
      'completed_requests': completed.length,
      'pending_requests': pending.length,
      'rejected_requests': rejected.length,
      'total_withdrawn': totalWithdrawn,
      'pending_amount': pendingAmount,
      'this_month_withdrawn': thisMonthAmount,
      'this_month_requests': thisMonth.length,
      'average_withdrawal': completed.isNotEmpty ? totalWithdrawn / completed.length : 0.0,
    };
  }

  // Get monthly withdrawal history
  Future<Map<String, double>> getMonthlyWithdrawals(int year) async {
    await Future.delayed(const Duration(milliseconds: 500));
    
    final monthlyWithdrawals = <String, double>{};
    
    for (int month = 1; month <= 12; month++) {
      final monthRequests = _dummyRequests.where((r) => 
          r.isCompleted &&
          r.processedAt != null &&
          r.processedAt!.year == year && 
          r.processedAt!.month == month).toList();
      
      final monthTotal = monthRequests.fold(0.0, (sum, r) => sum + r.amount);
      
      final monthNames = [
        'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
        'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
      ];
      
      monthlyWithdrawals[monthNames[month - 1]] = monthTotal;
    }
    
    return monthlyWithdrawals;
  }

  // Validate withdrawal request
  Future<Map<String, dynamic>> validateWithdrawalRequest({
    required double amount,
    required double availableBalance,
  }) async {
    await Future.delayed(const Duration(milliseconds: 200));
    
    final errors = <String>[];
    final warnings = <String>[];
    
    // Minimum amount check
    if (amount < 50.0) {
      errors.add('الحد الأدنى للسحب هو 50 ريال');
    }
    
    // Maximum amount check
    if (amount > 2000.0) {
      warnings.add('المبالغ الكبيرة قد تحتاج وقت أطول للمعالجة');
    }
    
    // Available balance check
    if (amount > availableBalance) {
      errors.add('المبلغ المطلوب يتجاوز الرصيد المتاح');
    }
    
    // Pending requests check
    final pendingRequests = await getPendingRequests();
    if (pendingRequests.isNotEmpty) {
      warnings.add('لديك طلبات سحب قيد المعالجة');
    }
    
    return {
      'is_valid': errors.isEmpty,
      'errors': errors,
      'warnings': warnings,
      'estimated_processing_time': amount > 1000 ? '3-5 أيام عمل' : '1-2 يوم عمل',
    };
  }
}
