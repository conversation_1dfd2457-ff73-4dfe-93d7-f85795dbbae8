class Package {
  final int id;
  final String name;
  final String description;
  final double price;
  final String type;
  final String packageScope;
  final int lectureCredits;
  final Subject? subject;
  final EducationStage? educationStage;

  Package({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.type,
    required this.packageScope,
    required this.lectureCredits,
    this.subject,
    this.educationStage,
  });

  // Add copyWith method for immutability
  Package copyWith({
    int? id,
    String? name,
    String? description,
    double? price,
    String? type,
    String? packageScope,
    int? lectureCredits,
    Subject? subject,
    EducationStage? educationStage,
  }) {
    return Package(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      price: price ?? this.price,
      type: type ?? this.type,
      packageScope: packageScope ?? this.packageScope,
      lectureCredits: lectureCredits ?? this.lectureCredits,
      subject: subject ?? this.subject,
      educationStage: educationStage ?? this.educationStage,
    );
  }

  factory Package.fromJson(Map<String, dynamic> json) {
    return Package(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      price: double.tryParse(json['price']?.toString() ?? '0') ?? 0.0,
      type: json['type'] ?? '',
      packageScope: json['package_scope'] ?? '',
      lectureCredits: json['lecture_credits'] ?? 0,
      subject: json['subject'] != null ? Subject.fromJson(json['subject']) : null,
      educationStage: json['education_stage'] != null ? EducationStage.fromJson(json['education_stage']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'price': price.toString(),
      'type': type,
      'package_scope': packageScope,
      'lecture_credits': lectureCredits,
      'subject': subject?.toJson(),
      'education_stage': educationStage?.toJson(),
    };
  }
}

// Add EducationStage model
class EducationStage {
  final int id;
  final String name;

  EducationStage({
    required this.id,
    required this.name,
  });

  factory EducationStage.fromJson(Map<String, dynamic> json) {
    return EducationStage(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
    };
  }
}


