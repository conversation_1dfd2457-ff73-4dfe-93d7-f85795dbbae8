import 'package:eschool/data/models/subject.dart';

class Package {
  final int id;
  final String name;
  final String description;
  final double price;
  final String type;
  final String packageScope;
  final int lectureCredits;
  final Subject? subject;
  final EducationStage? educationStage;
  final String title;
  final int numberOfLectures;
  final bool isPopular;
  final List<String> features;

  Package({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.type,
    required this.packageScope,
    required this.lectureCredits,
    this.subject,
    this.educationStage,
    String? title,
    int? numberOfLectures,
    this.isPopular = false,
    this.features = const [],
  }) : title = title ?? name,
       numberOfLectures = numberOfLectures ?? lectureCredits;

  // Add copyWith method for immutability
  Package copyWith({
    int? id,
    String? name,
    String? description,
    double? price,
    String? type,
    String? packageScope,
    int? lectureCredits,
    Subject? subject,
    EducationStage? educationStage,
    String? title,
    int? numberOfLectures,
    bool? isPopular,
    List<String>? features,
  }) {
    return Package(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      price: price ?? this.price,
      type: type ?? this.type,
      packageScope: packageScope ?? this.packageScope,
      lectureCredits: lectureCredits ?? this.lectureCredits,
      subject: subject ?? this.subject,
      educationStage: educationStage ?? this.educationStage,
      title: title ?? this.title,
      numberOfLectures: numberOfLectures ?? this.numberOfLectures,
      isPopular: isPopular ?? this.isPopular,
      features: features ?? this.features,
    );
  }

  factory Package.fromJson(Map<String, dynamic> json) {
    return Package(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      price: double.tryParse(json['price']?.toString() ?? '0') ?? 0.0,
      type: json['type'] ?? '',
      packageScope: json['package_scope'] ?? '',
      lectureCredits: json['lecture_credits'] ?? 0,
      subject: json['subject'] != null ? Subject.fromJson(json['subject']) : null,
      educationStage: json['education_stage'] != null ? EducationStage.fromJson(json['education_stage']) : null,
      title: json['title'] ?? json['name'] ?? '',
      numberOfLectures: json['number_of_lectures'] ?? json['lecture_credits'] ?? 0,
      isPopular: json['is_popular'] ?? false,
      features: json['features'] != null ? List<String>.from(json['features']) : [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'price': price.toString(),
      'type': type,
      'package_scope': packageScope,
      'lecture_credits': lectureCredits,
      'subject': subject?.toJson(),
      'education_stage': educationStage?.toJson(),
      'title': title,
      'number_of_lectures': numberOfLectures,
      'is_popular': isPopular,
      'features': features,
    };
  }

  // Helper methods for filtering
  bool hasSubject(int subjectId) {
    return subject?.id == subjectId;
  }

  bool hasLevel(String level) {
    return educationStage?.name.toLowerCase() == level.toLowerCase();
  }

  bool hasLevelForSubject(int subjectId, String level) {
    return hasSubject(subjectId) && hasLevel(level);
  }

  // Static helper method for finding subject ID by name
  static int findSubjectIdByName(String? subjectName) {
    if (subjectName == null || subjectName.isEmpty) return 0;

    // This is a simplified mapping - in a real app, you'd get this from a repository
    final subjectMap = {
      'mathematics': 1,
      'science': 2,
      'english': 3,
      'arabic': 4,
      'physics': 5,
      'chemistry': 6,
      'biology': 7,
      'history': 8,
      'geography': 9,
    };

    return subjectMap[subjectName.toLowerCase()] ?? 0;
  }
}

// Add EducationStage model
class EducationStage {
  final int id;
  final String name;

  EducationStage({
    required this.id,
    required this.name,
  });

  factory EducationStage.fromJson(Map<String, dynamic> json) {
    return EducationStage(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
    };
  }
}


