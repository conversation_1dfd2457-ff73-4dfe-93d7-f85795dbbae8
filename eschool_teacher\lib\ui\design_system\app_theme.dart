import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'app_design_system.dart';

/// Theme configuration for the ESchool Teacher App Design System
/// 
/// This file provides Flutter ThemeData configurations that use the
/// design system tokens adapted for the teacher app to ensure consistency
/// while supporting Arabic RTL layout and teacher-specific features.

class AppTheme {
  AppTheme._();

  /// Light theme configuration for teacher app
  static ThemeData get lightTheme => ThemeData(
    useMaterial3: true,
    brightness: Brightness.light,
    
    // Color scheme
    colorScheme: ColorScheme.fromSeed(
      seedColor: AppColors.primaryColor,
      brightness: Brightness.light,
      primary: AppColors.primaryColor,
      secondary: AppColors.secondaryColor,
      surface: AppColors.surfaceColor,
      background: AppColors.pageBackgroundColor,
      error: AppColors.errorColor,
      onPrimary: AppColors.onPrimaryColor,
      onSecondary: AppColors.onSecondaryColor,
      onSurface: AppColors.onSurfaceColor,
      onBackground: AppColors.arabicTextColor,
      onError: AppColors.onPrimaryColor,
    ),
    
    // Typography with Arabic support
    textTheme: GoogleFonts.poppinsTextTheme().copyWith(
      displayLarge: AppTypography.heading1,
      displayMedium: AppTypography.heading2,
      displaySmall: AppTypography.heading3,
      headlineLarge: AppTypography.heading2,
      headlineMedium: AppTypography.heading3,
      headlineSmall: AppTypography.heading4,
      titleLarge: AppTypography.heading3,
      titleMedium: AppTypography.heading4,
      titleSmall: AppTypography.labelLarge,
      bodyLarge: AppTypography.bodyLarge,
      bodyMedium: AppTypography.bodyMedium,
      bodySmall: AppTypography.bodySmall,
      labelLarge: AppTypography.labelLarge,
      labelMedium: AppTypography.labelMedium,
      labelSmall: AppTypography.labelSmall,
    ),
    
    // App Bar theme (teacher-specific)
    appBarTheme: AppBarTheme(
      backgroundColor: AppColors.primaryColor,
      foregroundColor: AppColors.onPrimaryColor,
      elevation: 2,
      shadowColor: AppColors.primaryColor.withOpacity(0.3),
      titleTextStyle: AppTypography.heading3.copyWith(
        color: AppColors.onPrimaryColor,
      ),
      centerTitle: true,
      iconTheme: const IconThemeData(
        color: AppColors.onPrimaryColor,
        size: AppComponents.iconMedium,
      ),
    ),
    
    // Button themes
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.primaryColor,
        foregroundColor: AppColors.onPrimaryColor,
        disabledBackgroundColor: AppColors.disabledColor,
        disabledForegroundColor: AppColors.onSurfaceColor,
        elevation: 2,
        shadowColor: AppColors.primaryColor.withOpacity(0.3),
        shape: RoundedRectangleBorder(
          borderRadius: AppBorderRadius.button,
        ),
        padding: AppSpacing.buttonPadding,
        textStyle: AppTypography.buttonMedium,
        minimumSize: const Size(0, AppComponents.buttonHeight),
      ),
    ),
    
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: AppColors.primaryColor,
        disabledForegroundColor: AppColors.disabledColor,
        side: const BorderSide(
          color: AppColors.primaryColor,
          width: 1.5,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: AppBorderRadius.button,
        ),
        padding: AppSpacing.buttonPadding,
        textStyle: AppTypography.buttonMedium,
        minimumSize: const Size(0, AppComponents.buttonHeight),
      ),
    ),
    
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: AppColors.primaryColor,
        disabledForegroundColor: AppColors.disabledColor,
        textStyle: AppTypography.buttonMedium,
        padding: const EdgeInsets.symmetric(
          horizontal: AppSpacing.medium,
          vertical: AppSpacing.xSmall,
        ),
      ),
    ),
    
    // Input decoration theme (RTL-optimized)
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: AppColors.surfaceColor,
      hintStyle: AppTypography.hint,
      labelStyle: AppTypography.labelMedium,
      errorStyle: AppTypography.error,
      helperStyle: AppTypography.caption,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      border: OutlineInputBorder(
        borderRadius: AppBorderRadius.roundedLarge,
        borderSide: const BorderSide(
          color: AppColors.borderColor,
          width: 1.0,
        ),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: AppBorderRadius.roundedLarge,
        borderSide: const BorderSide(
          color: AppColors.borderColor,
          width: 1.0,
        ),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: AppBorderRadius.roundedLarge,
        borderSide: const BorderSide(
          color: AppColors.primaryColor,
          width: 2.0,
        ),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: AppBorderRadius.roundedLarge,
        borderSide: const BorderSide(
          color: AppColors.errorColor,
          width: 1.0,
        ),
      ),
    ),
    
    // Card theme (teacher-specific)
    cardTheme: CardTheme(
      color: AppColors.cardBackgroundColor,
      elevation: 2,
      shadowColor: AppColors.primaryColor.withOpacity(0.1),
      shape: RoundedRectangleBorder(
        borderRadius: AppBorderRadius.teacherCard,
      ),
      margin: const EdgeInsets.all(AppSpacing.xSmall),
    ),
    
    // Bottom sheet theme
    bottomSheetTheme: BottomSheetThemeData(
      backgroundColor: AppColors.pageBackgroundColor,
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: AppBorderRadius.bottomSheet,
      ),
      clipBehavior: Clip.antiAlias,
    ),
    
    // Dialog theme
    dialogTheme: DialogTheme(
      backgroundColor: AppColors.pageBackgroundColor,
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: AppBorderRadius.roundedLarge,
      ),
      titleTextStyle: AppTypography.heading3,
      contentTextStyle: AppTypography.bodyMedium,
    ),
    
    // Divider theme
    dividerTheme: const DividerThemeData(
      color: AppColors.borderColor,
      thickness: 1,
      space: 1,
    ),
    
    // Icon theme
    iconTheme: const IconThemeData(
      color: AppColors.onSurfaceColor,
      size: AppComponents.iconMedium,
    ),
    
    // List tile theme
    listTileTheme: ListTileThemeData(
      contentPadding: const EdgeInsets.symmetric(
        horizontal: AppSpacing.medium,
        vertical: AppSpacing.xSmall,
      ),
      titleTextStyle: AppTypography.bodyLarge,
      subtitleTextStyle: AppTypography.bodySmall,
      leadingAndTrailingTextStyle: AppTypography.labelMedium,
      iconColor: AppColors.onSurfaceColor,
      shape: RoundedRectangleBorder(
        borderRadius: AppBorderRadius.roundedMedium,
      ),
    ),
    
    // Progress indicator theme
    progressIndicatorTheme: const ProgressIndicatorThemeData(
      color: AppColors.primaryColor,
      linearTrackColor: AppColors.borderColor,
      circularTrackColor: AppColors.borderColor,
    ),
    
    // Snack bar theme
    snackBarTheme: SnackBarThemeData(
      backgroundColor: AppColors.secondaryColor,
      contentTextStyle: AppTypography.bodyMedium.copyWith(
        color: AppColors.onSecondaryColor,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: AppBorderRadius.roundedMedium,
      ),
      behavior: SnackBarBehavior.floating,
      elevation: 4,
    ),
    
    // Floating action button theme
    floatingActionButtonTheme: FloatingActionButtonThemeData(
      backgroundColor: AppColors.primaryColor,
      foregroundColor: AppColors.onPrimaryColor,
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: AppBorderRadius.roundedLarge,
      ),
    ),
  );
  
  /// Dark theme configuration (using light theme for now)
  static ThemeData get darkTheme => lightTheme;
}

/// Extension methods for easy theme access in teacher app
extension AppThemeExtension on ThemeData {
  /// Get app-specific colors
  AppColors get appColors => AppDesignSystem.colors;
  
  /// Get app-specific typography
  AppTypography get appTypography => AppDesignSystem.typography;
  
  /// Get app-specific spacing
  AppSpacing get appSpacing => AppDesignSystem.spacing;
  
  /// Get app-specific border radius
  AppBorderRadius get appBorderRadius => AppDesignSystem.borderRadius;
  
  /// Get app-specific shadows
  AppShadows get appShadows => AppDesignSystem.shadows;
  
  /// Get app-specific component configurations
  AppComponents get appComponents => AppDesignSystem.components;
}
