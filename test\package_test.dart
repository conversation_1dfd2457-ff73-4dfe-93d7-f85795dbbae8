import 'package:flutter_test/flutter_test.dart';
import 'package:eschool/data/models/package.dart';
import 'package:eschool/data/models/subject.dart';
import 'package:eschool/data/models/subject_level.dart';
import 'package:eschool/data/repositories/packageRepository.dart';

void main() {
  group('Package Model Tests', () {
    test('Package creation with all required fields', () {
      final subject = Subject(
        id: 1,
        title: 'Arabic',
        imageUrl: 'https://example.com/image.jpg',
        subjectLevels: [
          SubjectLevel(id: 1, name: 'Primary', subjectId: 1),
        ],
      );

      final educationStage = EducationStage(id: 1, name: 'Primary');

      final package = Package(
        id: 1,
        name: 'Arabic Package',
        description: 'Learn Arabic fundamentals',
        price: 100.0,
        type: 'package',
        packageScope: 'individual',
        lectureCredits: 5,
        subject: subject,
        educationStage: educationStage,
      );

      expect(package.id, 1);
      expect(package.name, 'Arabic Package');
      expect(package.description, 'Learn Arabic fundamentals');
      expect(package.price, 100.0);
      expect(package.type, 'package');
      expect(package.packageScope, 'individual');
      expect(package.lectureCredits, 5);
      expect(package.subject?.id, 1);
      expect(package.educationStage?.id, 1);
    });

    test('Package JSON serialization and deserialization', () {
      final originalPackage = Package(
        id: 1,
        name: 'Test Package',
        description: 'Test Description',
        price: 50.0,
        type: 'package',
        packageScope: 'group',
        lectureCredits: 3,
        subject: Subject(
          id: 2,
          title: 'English',
          imageUrl: 'https://example.com/english.jpg',
        ),
        educationStage: EducationStage(id: 2, name: 'Middle'),
      );

      // Convert to JSON
      final json = originalPackage.toJson();

      // Convert back from JSON
      final packageFromJson = Package.fromJson(json);

      expect(packageFromJson.id, originalPackage.id);
      expect(packageFromJson.name, originalPackage.name);
      expect(packageFromJson.description, originalPackage.description);
      expect(packageFromJson.price, originalPackage.price);
      expect(packageFromJson.type, originalPackage.type);
      expect(packageFromJson.packageScope, originalPackage.packageScope);
      expect(packageFromJson.lectureCredits, originalPackage.lectureCredits);
      expect(packageFromJson.subject?.id, originalPackage.subject?.id);
      expect(packageFromJson.educationStage?.id, originalPackage.educationStage?.id);
    });

    test('Package with null optional fields', () {
      final package = Package(
        id: 1,
        name: 'Basic Package',
        description: 'Basic package description',
        price: 25.0,
        type: 'package',
        packageScope: 'individual',
        lectureCredits: 2,
        subject: null,
        educationStage: null,
      );

      expect(package.subject, isNull);
      expect(package.educationStage, isNull);
    });
  });

  group('EducationStage Tests', () {
    test('EducationStage creation and JSON handling', () {
      final stage = EducationStage(id: 1, name: 'Primary');
      
      expect(stage.id, 1);
      expect(stage.name, 'Primary');

      final json = stage.toJson();
      final stageFromJson = EducationStage.fromJson(json);

      expect(stageFromJson.id, stage.id);
      expect(stageFromJson.name, stage.name);
    });
  });

  group('PackageRepository Tests', () {
    test('getDummyPackages returns valid packages', () {
      final repository = PackageRepository();
      final packages = repository.getDummyPackages();

      expect(packages, isNotEmpty);
      expect(packages.length, 4);

      // Check first package
      final firstPackage = packages.first;
      expect(firstPackage.id, 1);
      expect(firstPackage.name, 'Arabic');
      expect(firstPackage.subject?.id, 1);
      expect(firstPackage.educationStage?.id, 1);
    });

    test('getDummyPackages with subject filter', () {
      final repository = PackageRepository();
      final packages = repository.getDummyPackages(subjectId: 1);

      expect(packages, isNotEmpty);
      // Should only return packages with subject id 1
      for (final package in packages) {
        expect(package.subject?.id, 1);
      }
    });

    test('getDummyPackages with education stage filter', () {
      final repository = PackageRepository();
      final packages = repository.getDummyPackages(educationStageId: 2);

      expect(packages, isNotEmpty);
      // Should only return packages with education stage id 2
      for (final package in packages) {
        expect(package.educationStage?.id, 2);
      }
    });

    test('getDummyPackages with both filters', () {
      final repository = PackageRepository();
      final packages = repository.getDummyPackages(subjectId: 1, educationStageId: 2);

      expect(packages, isNotEmpty);
      // Should only return packages matching both criteria
      for (final package in packages) {
        expect(package.subject?.id, 1);
        expect(package.educationStage?.id, 2);
      }
    });
  });
}
