import 'package:eschool/data/models/cart/cart_item.dart';
import 'package:eschool/data/models/classLesson.dart';
import 'package:eschool/data/models/package.dart';
import 'package:hive/hive.dart';

// We'll use manual adapters instead of generated ones
// part 'package_cart_item.g.dart';

/// Cart item for lecture packages
@HiveType(typeId: 11)
class PackageCartItem extends CartItem {
  @HiveField(5)
  final int numberOfLectures;

  @HiveField(6)
  final List<String> selectedLessonIds;

  @HiveField(7)
  final String packageId;

  @HiveField(8)
  final String? courseFilter;

  @HiveField(9)
  final String? levelFilter;

  const PackageCartItem({
    required String id,
    required String title,
    required double price,
    required int quantity,
    required this.numberOfLectures,
    required this.selectedLessonIds,
    required this.packageId,
    this.courseFilter,
    this.levelFilter,
    bool isSelected = true,
  }) : super(
          id: id,
          title: title,
          price: price,
          quantity: quantity,
          itemType: 'package',
          isSelected: isSelected,
        );

  /// Create a PackageCartItem from a Package and selected lessons
  factory PackageCartItem.fromPackage({
    required Package package,
    required List<ClassLesson> selectedLessons,
    String? subjectName,
    String? educationStageName,
  }) {
    return PackageCartItem(
      id: 'package_${package.id}_${DateTime.now().millisecondsSinceEpoch}',
      title: package.name,
      price: package.price,
      quantity: 1,
      numberOfLectures: package.lectureCredits,
      selectedLessonIds: selectedLessons.map((lesson) => lesson.id).toList(),
      packageId: package.id.toString(),
      courseFilter: subjectName,
      levelFilter: educationStageName,
    );
  }

  @override
  CartItem copyWith({
    String? id,
    String? title,
    double? price,
    int? quantity,
    String? itemType,
    bool? isSelected,
    int? numberOfLectures,
    List<String>? selectedLessonIds,
    String? packageId,
    String? courseFilter,
    String? levelFilter,
  }) {
    return PackageCartItem(
      id: id ?? this.id,
      title: title ?? this.title,
      price: price ?? this.price,
      quantity: quantity ?? this.quantity,
      isSelected: isSelected ?? this.isSelected,
      numberOfLectures: numberOfLectures ?? this.numberOfLectures,
      selectedLessonIds: selectedLessonIds ?? this.selectedLessonIds,
      packageId: packageId ?? this.packageId,
      courseFilter: courseFilter ?? this.courseFilter,
      levelFilter: levelFilter ?? this.levelFilter,
    );
  }

  @override
  List<Object?> get props => [
        ...super.props,
        numberOfLectures,
        selectedLessonIds,
        packageId,
        courseFilter,
        levelFilter,
      ];
}


