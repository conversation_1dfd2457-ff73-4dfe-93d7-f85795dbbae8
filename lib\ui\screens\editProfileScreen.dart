import 'dart:io';
import 'package:eschool/cubits/auth_cubit.dart';
import 'package:eschool/cubits/updateProfileCubit.dart';
import 'package:eschool/data/models/student.dart';
import 'package:eschool/data/repositories/profileRepository.dart';
import 'package:eschool/ui/design_system/app_design_system.dart';
import 'package:eschool/ui/design_system/components/app_components.dart';
import 'package:eschool/ui/widgets/customAppbar.dart';
import 'package:eschool/ui/widgets/customCircularProgressIndicator.dart';
import 'package:eschool/ui/widgets/customTextFieldContainer.dart';
import 'package:eschool/ui/widgets/customUserProfileImageWidget.dart';
import 'package:eschool/utils/labelKeys.dart';
import 'package:eschool/utils/uiUtils.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';

class EditProfileScreen extends StatefulWidget {
  final Student student;

  const EditProfileScreen({Key? key, required this.student}) : super(key: key);

  @override
  State<EditProfileScreen> createState() => _EditProfileScreenState();

  static Route route(RouteSettings routeSettings) {
    return MaterialPageRoute(
      builder: (_) => BlocProvider(
        create: (context) => UpdateProfileCubit(ProfileRepository()),
        child: EditProfileScreen(
          student: routeSettings.arguments as Student,
        ),
      ),
    );
  }
}

class _EditProfileScreenState extends State<EditProfileScreen> {
  late TextEditingController _nameController;
  late TextEditingController _phoneController;
  File? _selectedProfileImage;
  final ImagePicker _picker = ImagePicker();

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.student.name);
    _phoneController = TextEditingController(text: widget.student.mobile);
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  Future<void> _pickImage() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 70, // Reduce image quality to make upload faster
        maxWidth: 800,    // Limit image size
      );

      if (image != null && mounted) {
        debugPrint("Image picked: ${image.path}");
        setState(() {
          _selectedProfileImage = File(image.path);
        });
      }
    } catch (e) {
      debugPrint("Error picking image: $e");
      if (mounted) {
        UiUtils.showCustomSnackBar(
          context: context,
          errorMessage: UiUtils.getTranslatedLabel(context, "errorUploadingImage"),
          backgroundColor: Theme.of(context).colorScheme.error,
        );
      }
    }
  }

  void _updateProfile() {
    if (_nameController.text.trim().isEmpty) {
      UiUtils.showCustomSnackBar(
        context: context,
        errorMessage: UiUtils.getTranslatedLabel(context, "nameCannotBeEmpty"),
        backgroundColor: Theme.of(context).colorScheme.error,
      );
      return;
    }

    if (_phoneController.text.trim().isEmpty) {
      UiUtils.showCustomSnackBar(
        context: context,
        errorMessage: UiUtils.getTranslatedLabel(context, "phoneCannotBeEmpty"),
        backgroundColor: Theme.of(context).colorScheme.error,
      );
      return;
    }

    context.read<UpdateProfileCubit>().updateProfile(
          name: _nameController.text.trim(),
          phoneNumber: _phoneController.text.trim(),
          profileImage: _selectedProfileImage,
        );
  }

  Widget _buildProfileImage() {
    return Stack(
      children: [
        Container(
          width: 120,
          height: 120,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Theme.of(context).colorScheme.primary,
          ),
          child: _selectedProfileImage != null
              ? ClipOval(
                  child: Image.file(
                    _selectedProfileImage!,
                    fit: BoxFit.cover,
                    width: 120,
                    height: 120,
                  ),
                )
              : CustomUserProfileImageWidget(
                  profileUrl: widget.student.image,
                ),
        ),
        Positioned(
          bottom: 0,
          right: 0,
          child: InkWell(
            onTap: _pickImage,
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppColors.primaryColor,
                shape: BoxShape.circle,
                border: Border.all(
                  color: Colors.white,
                  width: 2,
                ),
              ),
              child: const Icon(
                Icons.camera_alt,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Padding(
            padding: EdgeInsets.only(
              top: UiUtils.getScrollViewTopPadding(
                context: context,
                appBarHeightPercentage: UiUtils.appBarSmallerHeightPercentage,
              ),
            ),
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(
                horizontal: MediaQuery.of(context).size.width * 0.06,
                vertical: 20,
              ),
              child: Column(
                children: [
                  _buildProfileImage(),
                  const SizedBox(height: 30),
                  CustomTextFieldContainer(
                    textEditingController: _nameController,
                    hintTextKey: "fullName",
                    hideText: false,
                  ),
                  const SizedBox(height: 20),
                  CustomTextFieldContainer(
                    textEditingController: _phoneController,
                    hintTextKey: "phoneNumber",
                    hideText: false,
                    keyboardType: TextInputType.phone,
                  ),
                  const SizedBox(height: 40),
                  BlocConsumer<UpdateProfileCubit, UpdateProfileState>(
                    listener: (context, state) {
                      if (state is UpdateProfileSuccess) {
                        // Update the student profile in AuthCubit
                        context
                            .read<AuthCubit>()
                            .updateStudentProfile(student: state.student);

                        UiUtils.showCustomSnackBar(
                          context: context,
                          errorMessage: UiUtils.getTranslatedLabel(
                              context, "profileUpdatedSuccessfully"),
                          backgroundColor: Colors.green,
                        );

                        Navigator.of(context).pop();
                      } else if (state is UpdateProfileFailure) {
                        UiUtils.showCustomSnackBar(
                          context: context,
                          errorMessage: UiUtils.getTranslatedLabel(context, "errorUpdatingProfile"),
                          backgroundColor: Theme.of(context).colorScheme.error,
                        );
                        debugPrint("Error details: ${state.errorMessage}");
                      }
                    },
                    builder: (context, state) {
                      return AppCustomRoundedButton(
                        onTap: state is UpdateProfileInProgress
                            ? null
                            : _updateProfile,
                        widthPercentage: 0.8,
                        backgroundColor: AppColors.primaryColor,
                        buttonTitle:
                            UiUtils.getTranslatedLabel(context, "updateProfile"),
                        titleColor: Colors.white,
                        showBorder: false,
                        child: state is UpdateProfileInProgress
                            ? const CustomCircularProgressIndicator(
                                strokeWidth: 2,
                                widthAndHeight: 20,
                              )
                            : null,
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
          Align(
            alignment: Alignment.topCenter,
            child: CustomAppBar(
              title: UiUtils.getTranslatedLabel(context, "editProfile"),
            ),
          ),
        ],
      ),
    );
  }
}
