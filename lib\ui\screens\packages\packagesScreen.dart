import 'package:eschool/app/routes.dart';
import 'package:eschool/cubits/packagesCubit.dart';
import 'package:eschool/cubits/cart/cart_cubit.dart';
import 'package:eschool/cubits/auth_cubit.dart';
import 'package:eschool/data/models/package.dart';
import 'package:eschool/data/models/cart/package_cart_item.dart';
import 'package:eschool/data/models/student.dart';
import 'package:eschool/data/repositories/packageRepository.dart';
import 'package:eschool/ui/screens/packages/widgets/packageCard.dart';
import 'package:eschool/ui/styles/colors.dart';
import 'package:eschool/ui/widgets/customShimmerContainer.dart';
import 'package:eschool/ui/widgets/errorContainer.dart';
import 'package:eschool/ui/widgets/noDataContainer.dart';
import 'package:eschool/ui/widgets/shimmerLoadingContainer.dart';
import 'package:eschool/ui/widgets/standardTopBar.dart';
import 'package:eschool/utils/labelKeys.dart';
import 'package:eschool/utils/uiUtils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class PackagesScreen extends StatefulWidget {
  final String? courseFilter;
  final String? levelFilter;

  const PackagesScreen({
    super.key,
    this.courseFilter,
    this.levelFilter,
  });

  static Route route(RouteSettings routeSettings) {
    final arguments = routeSettings.arguments as Map<String, dynamic>?;
    return MaterialPageRoute(
      builder: (_) => BlocProvider(
        create: (context) => PackagesCubit(
          packageRepository: PackageRepository(),
          useDummyData: true, // Set to false when API is ready
        ),
        child: PackagesScreen(
          courseFilter: arguments?['courseFilter'],
          levelFilter: arguments?['levelFilter'],
        ),
      ),
    );
  }

  @override
  State<PackagesScreen> createState() => _PackagesScreenState();
}

class _PackagesScreenState extends State<PackagesScreen> {
  @override
  void initState() {
    super.initState();
    fetchPackages();
  }

  void fetchPackages() {
    // Debug information
    debugPrint("Fetching packages with filters:");
    debugPrint("courseFilter: ${widget.courseFilter}");
    debugPrint("levelFilter: ${widget.levelFilter}");

    Future.delayed(Duration.zero, () {
      if (mounted) {
        context.read<PackagesCubit>().fetchPackages(
          subjectId: Package.findSubjectIdByName(widget.courseFilter),
          educationStageId: null, // We'll handle level filtering in the UI for now
        );
      }
    });
  }

  // Get the number of available credits for the current subject and level
  int _getAvailableCredits() {
    if (context.read<AuthCubit>().isGuest()) {
      return 0;
    }

    final Student student = context.read<AuthCubit>().getStudentDetails();
    return student.getPointsFor(
      widget.courseFilter ?? "",
      widget.levelFilter ?? "",
    );
  }

  // Build the card for existing credits
  Widget _buildExistingCreditsCard() {
    // Check if the user is a guest
    if (context.read<AuthCubit>().isGuest()) {
      return const SizedBox.shrink();
    }

    final int availableCredits = _getAvailableCredits();
    final bool hasCredits = availableCredits > 0;

    return Card(
      elevation: 4,
      margin: const EdgeInsets.all(16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: hasCredits ? primaryColor : Colors.grey, width: 2),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Row(
              children: [
                Icon(
                  hasCredits ? Icons.credit_score : Icons.credit_card_off,
                  size: 28,
                  color: hasCredits ? primaryColor : Colors.grey,
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        hasCredits
                          ? UiUtils.getTranslatedLabel(context, useExistingCreditsKey)
                          : UiUtils.getTranslatedLabel(context, noCreditsForSubjectLevelKey),
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: hasCredits ? const Color(0xFF25536D) : Colors.grey.shade700,
                        ),
                      ),
                      const SizedBox(height: 4),
                      if (hasCredits)
                        Text(
                          "${UiUtils.getTranslatedLabel(context, youHaveAvailableCreditsKey)}: $availableCredits ${UiUtils.getTranslatedLabel(context, pointsKey)}",
                          style: TextStyle(
                            color: Colors.green.shade700,
                            fontWeight: FontWeight.w500,
                          ),
                        )
                      else
                        Text(
                          UiUtils.getTranslatedLabel(context, needToPurchasePackageKey),
                          style: TextStyle(
                            color: Colors.grey.shade600,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                    ],
                  ),
                ),
                if (hasCredits)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: primaryColor,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      UiUtils.getTranslatedLabel(context, recommendedKey),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              hasCredits
                ? UiUtils.getTranslatedLabel(context, bookLessonsWithExistingCreditsKey)
                : UiUtils.getTranslatedLabel(context, buyNewPackageToBookLessonsKey),
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 16),
            if (hasCredits)
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pushNamed(
                    Routes.classLessons,
                    arguments: {
                      'courseFilter': widget.courseFilter,
                      'levelFilter': widget.levelFilter,
                      'useExistingCredits': true,
                      'availableCredits': availableCredits,
                    },
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(
                  UiUtils.getTranslatedLabel(context, bookNowKey),
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildPackagesList() {
    return BlocBuilder<PackagesCubit, PackagesState>(
      builder: (context, state) {
        if (state is PackagesLoading) {
          return _buildPackagesShimmerLoading();
        }
        if (state is PackagesFetchFailure) {
          return Center(
            child: ErrorContainer(
              errorMessageText: state.errorMessage,
              onTapRetry: () {
                fetchPackages();
              },
            ),
          );
        }
        if (state is PackagesFetchSuccess) {
          if (state.packages.isEmpty) {
            return NoDataContainer(
              titleKey: noPackagesFoundKey,
              showRetryButton: true,
              onTapRetry: () {
                fetchPackages();
              },
            );
          }
          return ListView.builder(
            padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
            itemCount: state.packages.length,
            itemBuilder: (context, index) {
              final package = state.packages[index];

              // Filter packages based on subject and level
              bool matchesFilter = true;

              if (widget.courseFilter != null && matchesFilter) {
                // Convert subject name to ID
                final subjectId = Package.findSubjectIdByName(widget.courseFilter);
                if (subjectId != 0) {
                  matchesFilter = package.hasSubject(subjectId);
                }
              }

              if (widget.levelFilter != null && matchesFilter) {
                // Check if the level filter applies to the subject
                final subjectId = Package.findSubjectIdByName(widget.courseFilter);
                if (subjectId != 0) {
                  matchesFilter = package.hasLevelForSubject(subjectId, widget.levelFilter!);
                } else {
                  matchesFilter = package.hasLevel(widget.levelFilter!);
                }
              }

              // Skip this package if it doesn't match the filter
              if (!matchesFilter) {
                return const SizedBox.shrink();
              }

              return PackageCard(
                package: package,
                onTap: () {
                  // Create a package cart item using the factory constructor
                  final packageCartItem = PackageCartItem.fromPackage(
                    package: package,
                    selectedLessons: [], // No lessons selected yet
                    subjectName: widget.courseFilter,
                    educationStageName: widget.levelFilter,
                  );

                  // Add the package to cart and navigate directly to cart
                  context.read<CartCubit>().addItem(packageCartItem);

                  // Navigate to cart screen
                  Navigator.of(context).pushNamed(Routes.cart);
                },
              );
            },
          );
        }
        return const SizedBox();
      },
    );
  }

  Widget _buildPackagesShimmerLoading() {
    return ListView.builder(
      padding: EdgeInsets.only(
        left: MediaQuery.of(context).size.width * 0.05,
        right: MediaQuery.of(context).size.width * 0.05,
        top: 20.0,
        bottom: 20.0,
      ),
      itemCount: 3,
      itemBuilder: (context, index) {
        return ShimmerLoadingContainer(
          child: CustomShimmerContainer(
            height: 150,
            margin: const EdgeInsets.only(bottom: 20.0),
            borderRadius: 10.0,
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final currentLanguage = Localizations.localeOf(context).languageCode;

    // Determine the title based on filters
    String title = UiUtils.getTranslatedLabel(context, packagesKey);
    if (widget.courseFilter != null) {
      title = "${widget.courseFilter}";
      if (widget.levelFilter != null && widget.levelFilter!.isNotEmpty) {
        title += " - ${widget.levelFilter}";
      }
    }

    return Directionality(
      textDirection: UiUtils.getTextDirectionByLanguageCode(currentLanguage),
      child: Scaffold(
        appBar: StandardTopBar(
          title: title,
          showCartIcon: true,
        ),
        body: Column(
          children: [
            // Existing credits card - only shown if user has credits
            _buildExistingCreditsCard(),

            // Packages section title
            Padding(
              padding: const EdgeInsets.only(
                left: 16.0,
                right: 16.0,
                top: 16.0,
                bottom: 8.0,
              ),
              child: Text(
                UiUtils.getTranslatedLabel(context, selectPackageKey),
                style: const TextStyle(
                  fontSize: 18.0,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF25536D),
                ),
              ),
            ),

            // Packages list
            Expanded(
              child: _buildPackagesList(),
            ),
          ],
        ),
      ),
    );
  }
}