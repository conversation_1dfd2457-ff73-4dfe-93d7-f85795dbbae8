import 'dart:convert';
import 'package:eschool/data/models/learning_resource.dart';
import 'package:eschool/data/models/subject.dart';
import 'package:eschool/data/models/subject_level.dart';
import 'package:eschool/utils/api.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

class DigitalAssetRepository {
  // Cache key for digital assets
  static const String _cacheKey = 'digital_assets_cache';
  // Cache expiration time (in milliseconds) - default 1 hour
  static const int _cacheExpirationTime = 3600000; // 1 hour

  Future<List<LearningResource>> fetchDigitalAssets({int? subjectId, int? educationStageId}) async {
    try {
      // Check if we have valid cached data first
      final cachedData = await _getCachedDigitalAssets();
      if (cachedData != null) {
        debugPrint('DigitalAssetRepository: Using cached digital assets');

        // Apply filters to cached data if needed
        if (subjectId != null || educationStageId != null) {
          return _filterDigitalAssets(cachedData, subjectId, educationStageId);
        }

        return cachedData;
      }

      debugPrint('DigitalAssetRepository: Fetching digital assets from API');

      // Build query parameters if needed
      final Map<String, dynamic> queryParameters = {};
      if (subjectId != null) {
        queryParameters['subject_id'] = subjectId;
      }
      if (educationStageId != null) {
        queryParameters['education_stage_id'] = educationStageId;
      }

      // Make API call
      final result = await Api.get(
        url: Api.digitalAssets,
        useAuthToken: true,
        queryParameters: queryParameters.isNotEmpty ? queryParameters : null,
      );

      debugPrint('DigitalAssetRepository: API response: $result');

      // Handle the actual API response format (similar to packages)
      if (result['assets'] != null) {
        final List<dynamic> assetsData = result['assets'] as List;
        final List<LearningResource> resources = assetsData
            .map((asset) => LearningResource.fromJson(Map<String, dynamic>.from(asset)))
            .toList();

        debugPrint('DigitalAssetRepository: Successfully parsed ${resources.length} assets from API');

        // Cache the full result
        await _cacheDigitalAssets(resources);

        // Return filtered results
        return _filterDigitalAssets(resources, subjectId, educationStageId);
      } else {
        debugPrint('DigitalAssetRepository: No assets found in API response, falling back to dummy data');
      }

      // If there's an error in the API response, fall back to dummy data
      debugPrint('DigitalAssetRepository: API returned error: ${result['message']}');
      debugPrint('DigitalAssetRepository: Falling back to dummy data');
      return getDummyDigitalAssets();
    } catch (e) {
      debugPrint('DigitalAssetRepository: Error fetching digital assets: $e');
      debugPrint('DigitalAssetRepository: Falling back to dummy data');
      // Fall back to dummy data on error
      return getDummyDigitalAssets();
    }
  }

  // Filter digital assets based on subject and education stage
  List<LearningResource> _filterDigitalAssets(
    List<LearningResource> assets,
    int? subjectId,
    int? educationStageId
  ) {
    List<LearningResource> filtered = List.from(assets);

    if (subjectId != null) {
      filtered = filtered.where((asset) => asset.subject?.id == subjectId).toList();
    }

    if (educationStageId != null) {
      filtered = filtered.where((asset) => asset.educationStage?.id == educationStageId).toList();
    }

    return filtered;
  }

  // Cache digital assets locally
  Future<void> _cacheDigitalAssets(List<LearningResource> assets) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Serialize each LearningResource object to JSON
      final List<String> serializedAssets = assets
          .map((asset) => jsonEncode(asset.toJson()))
          .toList();

      // Save the list as a JSON string
      final Map<String, dynamic> cacheData = {
        'assets': serializedAssets,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };

      await prefs.setString(_cacheKey, jsonEncode(cacheData));

      debugPrint('DigitalAssetRepository: Cached ${assets.length} digital assets');
    } catch (e) {
      debugPrint('DigitalAssetRepository: Error caching digital assets: $e');
      // Continue without caching
    }
  }

  // Get cached digital assets if available and not expired
  Future<List<LearningResource>?> _getCachedDigitalAssets() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final String? cachedDataString = prefs.getString(_cacheKey);

      if (cachedDataString == null || cachedDataString.isEmpty) {
        return null; // No cache found
      }

      final Map<String, dynamic> cacheData = jsonDecode(cachedDataString);
      final int timestamp = cacheData['timestamp'];
      final int currentTime = DateTime.now().millisecondsSinceEpoch;

      // Check if cache is expired
      if (currentTime - timestamp > _cacheExpirationTime) {
        debugPrint('DigitalAssetRepository: Cache expired');
        return null;
      }

      // Deserialize the assets
      final List<dynamic> serializedAssets = cacheData['assets'];
      final List<LearningResource> assets = serializedAssets
          .map((assetJson) => LearningResource.fromJson(jsonDecode(assetJson)))
          .toList();

      debugPrint('DigitalAssetRepository: Found ${assets.length} cached digital assets');
      return assets;
    } catch (e) {
      debugPrint('DigitalAssetRepository: Error reading cached digital assets: $e');
      return null;
    }
  }

  // Invalidate cache (useful when forcing a refresh)
  Future<void> invalidateCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_cacheKey);
      debugPrint('DigitalAssetRepository: Cache invalidated');
    } catch (e) {
      debugPrint('DigitalAssetRepository: Error invalidating cache: $e');
    }
  }

  // Dummy data for testing/fallback
  List<LearningResource> getDummyDigitalAssets() {
    // Create dummy subjects
    final subjects = [
      Subject(
        id: 1,
        title: 'اللغة العربية',
        imageUrl: 'https://images.unsplash.com/photo-1503676260728-1c00da094a0b',
        subjectLevels: [
          SubjectLevel(id: 1, name: 'تأسيسي', subjectId: 1),
          SubjectLevel(id: 2, name: 'إعدادي', subjectId: 1),
          SubjectLevel(id: 3, name: 'ثانوي', subjectId: 1),
        ],
      ),
      Subject(
        id: 3,
        title: 'قرآن',
        imageUrl: 'https://images.unsplash.com/photo-1456513080510-7bf3a84b82f8',
        subjectLevels: [
          SubjectLevel(id: 5, name: 'حفظ', subjectId: 3),
          SubjectLevel(id: 6, name: 'تصحيح تلاوة', subjectId: 3),
        ],
      ),
      Subject(
        id: 4,
        title: 'لغة إنجليزية',
        imageUrl: 'https://images.unsplash.com/photo-1523050854058-8df90110c9f1',
        subjectLevels: [
          SubjectLevel(id: 7, name: 'تأسيسي', subjectId: 4),
          SubjectLevel(id: 8, name: 'إعدادي', subjectId: 4),
          SubjectLevel(id: 9, name: 'ثانوي', subjectId: 4),
        ],
      ),
    ];

    return [
      LearningResource(
        id: 1,
        title: "قواعد اللغة العربية - المستوى التأسيسي",
        description: "كتاب شامل لقواعد اللغة العربية للمبتدئين مع تمارين وأمثلة",
        price: 15.99,
        type: "digital_asset",
        currency: "QAR",
        imageUrl: "assets/images/arabic_grammar.jpg",
        fileUrl: "assets/files/arabic_grammar_basic.pdf",
        fileType: "PDF",
        fileSize: "5.2 MB",
        tags: ["اللغة العربية", "قواعد", "تأسيسي"],
        subject: subjects[0],
        level: "تأسيسي",
        uploadDate: DateTime.now().subtract(const Duration(days: 10)),
        uploadedBy: "أ. محمد أحمد",
      ),
      LearningResource(
        id: 2,
        title: "تجويد القرآن الكريم",
        description: "دليل شامل لأحكام التجويد مع أمثلة صوتية",
        price: 19.99,
        type: "digital_asset",
        currency: "QAR",
        imageUrl: "assets/images/quran_tajweed.jpg",
        fileUrl: "assets/files/tajweed_guide.pdf",
        fileType: "PDF",
        fileSize: "8.3 MB",
        tags: ["قرآن", "تجويد", "تصحيح تلاوة"],
        subject: subjects[1],
        level: "تصحيح تلاوة",
        uploadDate: DateTime.now().subtract(const Duration(days: 5)),
        uploadedBy: "الشيخ عبد الرحمن",
      ),
      LearningResource(
        id: 3,
        title: "English Grammar Basics",
        description: "Comprehensive guide to English grammar fundamentals",
        price: 12.99,
        type: "digital_asset",
        currency: "QAR",
        imageUrl: "assets/images/english_basics.jpg",
        fileUrl: "assets/files/english_grammar_basics.pdf",
        fileType: "PDF",
        fileSize: "4.1 MB",
        tags: ["English", "Grammar", "Basics"],
        subject: subjects[2],
        level: "تأسيسي",
        uploadDate: DateTime.now().subtract(const Duration(days: 15)),
        uploadedBy: "Ms. Sarah Johnson",
      ),
      LearningResource(
        id: 4,
        title: "النحو والصرف - المستوى المتقدم",
        description: "دراسة متقدمة في النحو والصرف مع تطبيقات عملية",
        price: 24.99,
        type: "digital_asset",
        currency: "QAR",
        imageUrl: "assets/images/arabic_advanced.jpg",
        fileUrl: "assets/files/nahw_sarf_advanced.pdf",
        fileType: "PDF",
        fileSize: "12.5 MB",
        tags: ["اللغة العربية", "نحو", "صرف", "متقدم"],
        subject: subjects[0],
        level: "ثانوي",
        uploadDate: DateTime.now().subtract(const Duration(days: 3)),
        uploadedBy: "د. أحمد الخطيب",
      ),
      LearningResource(
        id: 5,
        title: "حفظ القرآن - جزء عم",
        description: "برنامج متكامل لحفظ جزء عم مع التفسير الميسر",
        price: 9.99,
        type: "digital_asset",
        currency: "QAR",
        imageUrl: "assets/images/quran_memorization.jpg",
        fileUrl: "assets/files/juz_amma_memorization.pdf",
        fileType: "PDF",
        fileSize: "6.7 MB",
        tags: ["قرآن", "حفظ", "جزء عم"],
        subject: subjects[1],
        level: "حفظ",
        uploadDate: DateTime.now().subtract(const Duration(days: 20)),
        uploadedBy: "معهد القرآن الكريم",
      ),
    ];
  }
}