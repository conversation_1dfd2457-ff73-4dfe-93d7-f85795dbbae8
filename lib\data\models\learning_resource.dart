import 'package:eschool/data/models/subject.dart';
import 'package:eschool/data/models/package.dart'; // Import for EducationStage

class LearningResource {
  final int id;
  final String title;
  final String description;
  final double price;
  final String type; // Added to match API response
  final String currency;
  final String imageUrl;
  final String? fileUrl;
  final String fileType;
  final String fileSize;
  final List<String> tags;
  final Subject? subject; // Made nullable to match API
  final EducationStage? educationStage; // Added to match API structure
  final String level; // Keep for backward compatibility
  final bool isPurchased;
  final DateTime uploadDate;
  final String uploadedBy;

  LearningResource({
    required this.id,
    required this.title,
    required this.description,
    required this.price,
    required this.type,
    this.currency = 'QAR',
    this.imageUrl = '',
    this.fileUrl,
    this.fileType = '',
    this.fileSize = '',
    this.tags = const [],
    this.subject,
    this.educationStage,
    this.level = '',
    this.isPurchased = false,
    DateTime? uploadDate,
    this.uploadedBy = '',
  }) : uploadDate = uploadDate ?? DateTime.now();

  LearningResource copyWith({
    int? id,
    String? title,
    String? description,
    double? price,
    String? type,
    String? currency,
    String? imageUrl,
    String? fileUrl,
    String? fileType,
    String? fileSize,
    List<String>? tags,
    Subject? subject,
    EducationStage? educationStage,
    String? level,
    bool? isPurchased,
    DateTime? uploadDate,
    String? uploadedBy,
  }) {
    return LearningResource(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      price: price ?? this.price,
      type: type ?? this.type,
      currency: currency ?? this.currency,
      imageUrl: imageUrl ?? this.imageUrl,
      fileUrl: fileUrl ?? this.fileUrl,
      fileType: fileType ?? this.fileType,
      fileSize: fileSize ?? this.fileSize,
      tags: tags ?? this.tags,
      subject: subject ?? this.subject,
      educationStage: educationStage ?? this.educationStage,
      level: level ?? this.level,
      isPurchased: isPurchased ?? this.isPurchased,
      uploadDate: uploadDate ?? this.uploadDate,
      uploadedBy: uploadedBy ?? this.uploadedBy,
    );
  }

  factory LearningResource.fromJson(Map<String, dynamic> json) {
    // Handle education stage from API response
    EducationStage? educationStage;
    if (json['education_stage'] != null) {
      educationStage = EducationStage.fromJson(json['education_stage']);
    }

    // Handle subject from API response
    Subject? subject;
    if (json['subject'] != null) {
      subject = Subject.fromJson(json['subject']);
    }

    // Parse price from string or number
    double price = 0.0;
    if (json['price'] is String) {
      price = double.tryParse(json['price']) ?? 0.0;
    } else if (json['price'] is num) {
      price = (json['price'] as num).toDouble();
    }

    return LearningResource(
      id: json['id'] is String ? int.parse(json['id']) : json['id'] as int,
      title: json['name'] ?? json['title'] ?? '', // API uses 'name'
      description: json['description'] ?? '',
      price: price,
      type: json['type'] ?? 'digital_asset',
      currency: json['currency'] ?? 'QAR',
      imageUrl: json['imageUrl'] ?? json['image'] ?? '',
      fileUrl: json['fileUrl'] ?? json['file_url'],
      fileType: json['fileType'] ?? json['file_type'] ?? json['type'] ?? '',
      fileSize: json['fileSize'] ?? json['file_size'] ?? '',
      tags: json['tags'] != null ? List<String>.from(json['tags']) : [],
      subject: subject,
      educationStage: educationStage,
      level: educationStage?.name ?? json['level'] ?? '',
      isPurchased: json['isPurchased'] ?? json['is_purchased'] ?? false,
      uploadDate: json['uploadDate'] != null
          ? DateTime.parse(json['uploadDate'])
          : (json['upload_date'] != null
              ? DateTime.parse(json['upload_date'])
              : DateTime.now()),
      uploadedBy: json['uploadedBy'] ?? json['uploaded_by'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': title, // API expects 'name'
      'title': title,
      'description': description,
      'price': price.toString(),
      'type': type,
      'currency': currency,
      'imageUrl': imageUrl,
      'image': imageUrl,
      'fileUrl': fileUrl,
      'file_url': fileUrl,
      'fileType': fileType,
      'file_type': fileType,
      'fileSize': fileSize,
      'file_size': fileSize,
      'tags': tags,
      'subject': subject?.toJson(),
      'education_stage': educationStage?.toJson(),
      'level': level,
      'isPurchased': isPurchased,
      'is_purchased': isPurchased,
      'uploadDate': uploadDate.toIso8601String(),
      'upload_date': uploadDate.toIso8601String(),
      'uploadedBy': uploadedBy,
      'uploaded_by': uploadedBy,
    };
  }
}